<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data noupdate="1">
    <record id="op_1" model="emr_frm.asmt_option">
        <field name="name">Less than 3 years old</field>
        <field name="scale">4</field>
        <field name="criteria_id" ref="hdfs_c_1"/>
    </record>
    <record id="op_2" model="emr_frm.asmt_option">
        <field name="name">3 to less than 7 years old</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_1"/>
    </record>
    <record id="op_3" model="emr_frm.asmt_option">
        <field name="name">7 to less than 13 years old</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_1"/>
    </record>
    <record id="op_4" model="emr_frm.asmt_option">
        <field name="name">13 years and above</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_1"/>
    </record>

    <record id="op_5" model="emr_frm.asmt_option">
        <field name="name">Male</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_2"/>
    </record>
    <record id="op_6" model="emr_frm.asmt_option">
        <field name="name">Female</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_2"/>
    </record>

    <record id="op_7" model="emr_frm.asmt_option">
        <field name="name">Neurological Diagnosis</field>
        <field name="scale">4</field>
        <field name="criteria_id" ref="hdfs_c_3"/>
    </record>
    <record id="op_8" model="emr_frm.asmt_option">
        <field name="name">Alterations in Oxygenation (Respiratory Diagnosis, Dehydration, Anemia, Anorexia, syncope/Dizziness, etc.)</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_3"/>
    </record>
    <record id="op_9" model="emr_frm.asmt_option">
        <field name="name">Psychological/Behavioral Disorders</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_3"/>
    </record>
     <record id="op_10" model="emr_frm.asmt_option">
        <field name="name">Other Diagnosis</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_3"/>
    </record>

    <record id="op_11" model="emr_frm.asmt_option">
        <field name="name">Not aware of limitation</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_4"/>
    </record>
    <record id="op_12" model="emr_frm.asmt_option">
        <field name="name">Forgets Limitation</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_4"/>
    </record>
    <record id="op_13" model="emr_frm.asmt_option">
        <field name="name">Oriented to own ability</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_4"/>
    </record>

    <record id="op_14" model="emr_frm.asmt_option">
        <field name="name">History of falls or Infant-Toddler placed in bed </field>
        <field name="scale">4</field>
        <field name="criteria_id" ref="hdfs_c_5"/>
    </record>
    <record id="op_15" model="emr_frm.asmt_option">
        <field name="name">Patient uses assistive devices or infant-toddler in crib or furniture/lighting(Tripled room)</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_5"/>
    </record>
    <record id="op_16" model="emr_frm.asmt_option">
        <field name="name">Patient placed in bed</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_5"/>
    </record>
    <record id="op_17" model="emr_frm.asmt_option">
        <field name="name">Outpatient area</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_5"/>
    </record>

    <record id="op_18" model="emr_frm.asmt_option">
        <field name="name">Within 24 hours</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_6"/>
    </record>
    <record id="op_19" model="emr_frm.asmt_option">
        <field name="name">within 48 hours</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_6"/>
    </record>
    <record id="op_20" model="emr_frm.asmt_option">
        <field name="name">More than 48 hours/None</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_6"/>
    </record>

    <record id="op_21" model="emr_frm.asmt_option">
        <field name="name">Multiple usage of Sedatives(excluding ICU patients sedated and paralyzed) Hypnotics,
            Barbiturates, Phenothiazines, Antidepressants, Laxatives/Diuretics, Narcotics</field>
        <field name="scale">3</field>
        <field name="criteria_id" ref="hdfs_c_7"/>
    </record>
    <record id="op_22" model="emr_frm.asmt_option">
        <field name="name">One of the medications listed above</field>
        <field name="scale">2</field>
        <field name="criteria_id" ref="hdfs_c_7"/>
    </record>
    <record id="op_23" model="emr_frm.asmt_option">
        <field name="name">Other medications/None</field>
        <field name="scale">1</field>
        <field name="criteria_id" ref="hdfs_c_7"/>
    </record>
    <record id="op_24" model="emr_frm.asmt_option">
        <field name="name">No</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_8"/>
    </record>
    <record id="op_25" model="emr_frm.asmt_option">
        <field name="name">Yes</field>
        <field name="scale">25</field>
        <field name="criteria_id" ref="mfs_c_8"/>
    </record>

    <record id="op_26" model="emr_frm.asmt_option">
        <field name="name">No</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_9"/>
    </record>
    <record id="op_27" model="emr_frm.asmt_option">
        <field name="name">Yes</field>
        <field name="scale">15</field>
        <field name="criteria_id" ref="mfs_c_9"/>
    </record>

    <record id="op_28" model="emr_frm.asmt_option">
        <field name="name">None/ Bed rest/ Nurse assist</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_10"/>
    </record>
    <record id="op_29" model="emr_frm.asmt_option">
        <field name="name">Crutches/ Stick/ frame</field>
        <field name="scale">15</field>
        <field name="criteria_id" ref="mfs_c_10"/>
    </record>
     <record id="op_30" model="emr_frm.asmt_option">
        <field name="name">Furniture/ walls</field>
        <field name="scale">30</field>
        <field name="criteria_id" ref="mfs_c_10"/>
    </record>

    <record id="op_31" model="emr_frm.asmt_option">
        <field name="name">No</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_11"/>
    </record>
    <record id="op_32" model="emr_frm.asmt_option">
        <field name="name">Yes</field>
        <field name="scale">20</field>
        <field name="criteria_id" ref="mfs_c_11"/>
    </record>

     <record id="op_33" model="emr_frm.asmt_option">
        <field name="name">Normal/ Bed rest/ Wheelchair</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_12"/>
    </record>
    <record id="op_34" model="emr_frm.asmt_option">
        <field name="name">Weak</field>
        <field name="scale">10</field>
        <field name="criteria_id" ref="mfs_c_12"/>
    </record>
    <record id="op_35" model="emr_frm.asmt_option">
        <field name="name">Impaired</field>
        <field name="scale">20</field>
        <field name="criteria_id" ref="mfs_c_12"/>
    </record>

    <record id="op_36" model="emr_frm.asmt_option">
        <field name="name">Oriented to own ability</field>
        <field name="scale">0</field>
        <field name="criteria_id" ref="mfs_c_13"/>
    </record>
    <record id="op_37" model="emr_frm.asmt_option">
        <field name="name">Over estimates/ forgets limitations</field>
        <field name="scale">15</field>
        <field name="criteria_id" ref="mfs_c_13"/>
    </record>
</data>
<!--    <record id="op_38" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--    </record>-->
<!--    <record id="op_39" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--    </record>-->

<!--     <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--     <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--     <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    -->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">Yes</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
<!--    <record id="op_" model="emr_frm.asmt_option">-->
<!--        <field name="name">No</field>-->
<!--        <field name="scale">0</field>-->
<!--        <field name="criteria_id" ref=""/>-->
<!--    </record>-->
</odoo>
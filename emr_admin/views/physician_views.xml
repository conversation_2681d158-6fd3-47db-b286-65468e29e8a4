<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--TODO[IMP]: <model_name> _view_form-->
    <record id="physician_view_form" model="ir.ui.view">
        <field name="name">emr_admin.physician.form</field>
        <field name="model">emr_admin.physician</field>
        <field name="arch" type="xml">
            <form duplicate="0">
                <sheet>
                     <div class="oe_button_box" name="button_box">
                        <button name="action_open_fav_services" type="object" class="oe_stat_button"
                                string="Favorite Services" icon="fa-star" />
                     </div>

                    <group>
                        <group>
                            <field name="code"/>
                            <field name="ar_name"/>
                            <field name="en_name"/>

                            <field name="status" groups="base.group_no_one"/>
                            <!--                            <field name="is_physician" invisible="1"/>-->

                        </group>
                        <group>
                            <field name="first_service"/>
                            <field name="consultatio_service"/>
                            <field name="follow_up_service"/>
                        </group>
                        <group>
                            <!--                            <field name="deal_type" />-->
                            <!--                            <field name="cash_only" />-->

                        </group>
                    </group>
                    <notebook>
                        <page string="Default Setting">
                            <group>
                                <group>
                                    <field name="default_specialty_id"/>
                                    <field name="default_department_id"/>
                                    <field name="clinic_ids" invisible="1"/>
                                    <field name="default_clinic_id" domain="[('clinic_type', '=', 'clinic'), ('activate', '=', True), ('id', 'in', clinic_ids)]" options="{'no_create': True}" />
                                    <field name="default_or_id"/>
                                    <field name="favorite_group_id" />
                                </group>
                                <group>
                                    <field name="user_id" domain="[('share','=',False)]"/>
                                    <field name="company_ids" widget="many2many_tags"
                                           attrs="{'invisible':[('user_id','=',False)]}"/>
                                </group>
                                <group>
                                    <field name="is_referring_physician"/>
                                </group>
                            </group>
                        </page>
                        <page string="Specialties">
                            <group>
                                <field name="specialty_ids" widget="many2many_tags"/>
                            </group>

                        </page>
                        <page string="Address and other info">
                            <group>
                                <group>
                                     <label for="street" string="Address"/>
                                    <div class="o_address_format">
<!--                                        <b style="color:black;">Address</b>-->
                                        <field name="street" class="o_address_street" placeholder="Street..."/>
                                        <field name="street2" placeholder="Street 2..."/>
                                        <field name="city" placeholder="City" class="o_address_city"/>
                                        <field name="state_id" class="o_address_state" placeholder="State"/>
                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                        <field name="country_id" placeholder="Country" class="o_address_country"/>
                                    </div>

                                </group>
                                <group>
                                    <field name="mobile"/>
                                    <field name="phone"/>
                                    <field name="religion"/>
                                    <field name="sex"/>

                                </group>
                            </group>

                        </page>
                        <page string="Signature">
                            <group>
                                <field name="signature" widget="image"/>
                            </group>
                        </page>
                        <page string="Customer Types" invisible="1">
                            <group>
                                <field name="partner_type_ids" widget="many2many_tags"/>
                            </group>
                        </page>
                        <page string="Standard Codes">
                            <group>
                                <group>
                                    <field name="role_id"/>
                                    <field name="practice_code_id"/>
                                    <field name="license_no"/>
                                </group>
                                <group>
                                    <field name="religion_id"/>
                                    <field name="gender_id"/>
                                </group>
                            </group>
                        </page>

                        <!--                        <page string="Company">-->

                        <!--                        </page>-->
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_tree-->
    <record id="physician_view_tree" model="ir.ui.view">
        <field name="name">emr_admin.physician.tree</field>
        <field name="mode">primary</field>
        <field name="model">emr_admin.physician</field>
        <field name="arch" type="xml">
            <tree string="Physician">
                <!--                <field name="activate" />-->
                <field name="code"/>
                <!--                <field name="display_name" />-->
                <field name="name"/>
                <field name="partner_name" invisible="1"/>
                <!--                <field name="ar_name" />-->
                <!--                <field name="name" />-->
                <!--                <field name="sex" />-->
                <!--                <field name="religion" />-->
                <!--                <field name="summary" />-->
                <field name="cash_only" optional="hide"/>
                <!--                <field name="partner_id" />-->
                <!--                <field name="level_id" />-->

                <!--                <field name="deal_type" />-->
                <field name="user_id" optional="hide"></field>
                <field name="default_clinic_id" optional="hide"></field>
                <field name="default_specialty_id" optional="hide"></field>
                <field name="default_department_id" optional="hide"></field>
            </tree>
        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_search-->
    <record id="physician_view_search" model="ir.ui.view">
        <field name="name">emr_admin.physician.search</field>
        <field name="mode">primary</field>
        <field name="model">emr_admin.physician</field>
        <!--        <field name="inherit_id" ref="base.view_res_partner_filter"/>-->
        <field name="inherit_id" ref=""/>
        <field name="arch" type="xml">
            <search>
                <filter string="Cash Only" name="cash_only" domain="[('cash_only','=',True)]"/>
                <field name="code"/>
                <!--                 <field name="display_name" />-->
                <!--                <field name="sex" />-->
                <!--                <field name="religion" />-->
                <!--                <field name="summary" />-->
                <!--                <field name="cash_only" />-->
                <!--                <field name="level_id" />-->
                <field name="deal_type"/>
                <field name="name"/>
                <field name="partner_name" />
                <!--                <field name="display_name" />-->
                <!--                <field name="dis_name" string="dis_name"/>-->
            </search>
            <!--            <xpath expr="//search/field[@name='display_name']" position="replace">-->
            <!--                <field name="dis_name" string="Name"/>-->
            <!--            </xpath>-->

        </field>
    </record>

    <!--TODO[IMP]: <model_name> _view_kanban-->


    <!--TODO[IMP]: <model_name> _view_calendar-->


    <!--TODO[IMP]: <model_name> _view_graph-->


    <!--TODO[IMP]: <model_name> _action-->
    <record id="physician_action" model="ir.actions.act_window">
        <field name="name">Physicians</field>
        <field name="res_model">emr_admin.physician</field>
        <field name="view_mode">tree,form,search</field>
        <!--        <field name="context">{'search_default_available': 1}</field>-->
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a model
            </p>
            <p>
                Create model
            </p>
        </field>
    </record>
</odoo>

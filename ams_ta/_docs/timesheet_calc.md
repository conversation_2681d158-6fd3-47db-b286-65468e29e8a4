
# Timesheet Calculation

call hierarchy for `action_execute_punch_log` method in the `PunchLog` model. Here's the complete call hierarchy:

1. Entry Point:
```python
def action_execute_punch_log(self):
    ...
```

2. Direct Method Calls:
```
action_execute_punch_log
├── _validate_punch_log (<PERSON>Log)
├── employee_id.get_available_employee_shift (Employee)
├── employee_id._get_day (Em<PERSON>loyee)
├── _get_day_time_unit (PunchLog)
│   ├── _get_time_unit (PunchLog)
│   └── _get_nearest_time_unit (PunchLog)
├── employee_id._prepare_timesheet_record_vals (Employee)
├── employee_id._get_or_create_timesheet (Employee)
├── employee_id._get_or_create_ts_time_unit (Employee)
└── _handle_checkin_checkout (PunchLog)
```

3. Detailed Breakdown by Model:

**PunchLog Model** (`ams_ta.punch_log`):
- `_validate_punch_log()`
- `_get_day_time_unit()`
- `_get_time_unit()`
- `_get_nearest_time_unit()`
- `_handle_checkin_checkout()`

**Employee Model** (`hr.employee`):
- `get_available_employee_shift()`
- `_get_day()`
- `_prepare_timesheet_record_vals()`
- `_get_or_create_timesheet()`
- `_get_or_create_ts_time_unit()`

4. Related Models Involved:
- `ams_ta.punch_log`
- `hr.employee`
- `ams_ta.timesheet`
- `ams_ta.ts_time_unit`
- `ams_ta.employee_shift`
- `ams_ta.day`

5. Key Database Operations:
- Search for existing timesheet records
- Create new timesheet records
- Update timesheet time units
- Update punch log states
- Handle overnight shift scenarios

This method is central to the attendance tracking system, processing punch logs and creating/updating the corresponding timesheet records.

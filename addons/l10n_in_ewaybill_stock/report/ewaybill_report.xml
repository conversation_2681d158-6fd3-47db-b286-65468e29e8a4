<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="report_ewaybill">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.internal_layout">
                        <div class="page">
                            <style>
                                table {
                                    width: 100%;
                                }
                                th, td {
                                  padding: 5px;
                                  font-size: 14px;
                                }
                            </style>
                            <t t-set="challan_states" t-value="('challan', 'pending')"/>
                            <table class="border-bottom border-dark">
                                <tr class="d-flex justify-content-between align-items-center px-4 py-3">
                                    <td style="width: 8rem;">
                                        <img t-att-src="image_data_uri(doc.company_id.logo)"
                                             style="max-width: 8rem; height: auto;" alt="Company Logo"/>
                                    </td>
                                    <td>
                                        <h4 t-if="doc.state in challan_states and doc.picking_id" style="margin-top: 1rem;">Delivery Challan</h4>
                                        <h4 t-else="" style="margin-top: 1rem;">e-Way Bill</h4>
                                    </td>
                                    <t t-if="doc.state not in challan_states">
                                        <td>
                                            <img t-if="doc.name != False"
                                                 t-att-src="'/report/barcode/QR/' + str(doc.name) + '/' + str(doc.company_id.vat) + '/' + str(doc.ewaybill_date)"
                                                 style="width: 6.25rem; height: 6.25rem;" alt="Barcode"/>
                                        </td>
                                    </t>
                                </tr>
                            </table>
                            <br/>

                            <t t-set="generate_json" t-value="doc._ewaybill_generate_direct_json()"/>
                            <t t-if="doc.state not in challan_states">
                                <h5>E-WAY BILL Details</h5>
                                <table class="text-nowrap">
                                    <tr>
                                        <td>eway Bill No: <t t-out="doc.name"/></td>
                                        <td>Generated Date: <t t-out="doc.ewaybill_date"/></td>
                                        <td>Generated By: <t t-out="doc.company_id.vat"/></td>
                                        <td><t t-if="doc.name != False"></t>
                                            Valid Upto: <t t-out="doc.ewaybill_expiry_date"/>
                                            <t t-if="doc.mode == '1'">
                                                (Vehicle Type is <t t-out="dict(doc._fields['vehicle_type'].selection).get(doc.vehicle_type)"/>)
                                            </t>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Mode:<t t-out="dict(doc._fields['mode']._description_selection(doc.env)).get(doc.mode, '')"/>
                                        </td>
                                        <td colspan="3">
                                            Approx Distance: <t t-out="doc.distance"/> km
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="white-space: normal; word-break: break-all;">
                                            Type: <t t-out="doc.supply_type"/> - <t t-out="doc.type_id.sub_type"/>-<t t-out="doc.type_description"/>
                                        </td>
                                        <td colspan="2">
                                            Document Details:<t t-out="doc.type_id.name"/> - <t t-out="doc.picking_id.name"/> - <t t-out="doc.ewaybill_date"/>
                                        </td>
                                        <td>Transaction Type:
                                            <t t-if="generate_json['transactionType'] == 2">
                                                Bill To - Ship To
                                            </t>
                                            <t t-elif="generate_json['transactionType'] == 3">
                                                Bill From - Dispatch From
                                            </t>
                                            <t t-elif="generate_json['transactionType'] == 4">
                                                Combination of 2 and 3
                                            </t>
                                            <t t-else="">
                                                Regular
                                            </t>
                                        </td>
                                    </tr>
                                </table>
                                <div class="border-bottom border-dark mt-3"/>
                                <br/>
                            </t>
                            <t t-else="">
                                <h5>Document Details</h5>
                                <table>
                                    <tr>
                                        <td>Document No.: <b><t t-out="doc.picking_id.name"/></b></td>
                                        <td>Generated Date: <t t-out="doc.picking_id.scheduled_date"/></td>
                                    </tr>
                                </table>
                                <div class="border-bottom border-dark mt-3"/>
                                <br/>
                            </t>

                            <h5>Address Details</h5>
                            <div>
                                <table>
                                    <tr>
                                        <td>From</td>
                                        <td/>
                                        <td>To</td>
                                    </tr>
                                    <tr>
                                        <td class="border border-dark">
                                            GSTIN: <t t-out="generate_json['fromGstin']"/><br/>
                                            <t t-out="doc.partner_bill_from_id.name"/><br/>
                                            <t t-out="doc.partner_bill_from_id.state_id.name"/><br/>
                                            <br/>
                                            ::Dispatch From:: <br/><br/>
                                            <span t-field="doc.partner_ship_from_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'/>
                                        </td>
                                        <td/>
                                        <td class="border border-dark">
                                            GSTIN: <t t-out="generate_json['toGstin']"/><br/>
                                            <t t-out="doc.partner_bill_to_id.name"/><br/>
                                            <t t-out="doc.partner_bill_to_id.state_id.name"/><br/>
                                            <br/>
                                            ::Ship To:: <br/><br/>
                                            <span t-field="doc.partner_ship_to_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="border-bottom border-dark mt-3"/>
                            <br/>
                            <h5>Goods Details</h5>
                            <table class="border border-dark">
                                <thead>
                                    <tr class="border-bottom border-dark">
                                        <th>HSN Code</th>
                                        <th>Product Description</th>
                                        <th>Quantity</th>
                                        <th>Taxable Amount Rs.</th>
                                        <th>Tax Rate (C+S+I+Cess+Cess Non Advol)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="items" t-value="generate_json['itemList']"/>
                                    <tr t-foreach="items" t-as="item" class="border-bottom border-dark">
                                        <td>
                                            <t t-out="item['hsnCode']"/>
                                        </td>
                                        <td>
                                            <t t-out="item['productDesc']"/>
                                        </td>
                                        <td>
                                            <t t-out="item['quantity']"/> <t t-out="item['qtyUnit']"/>
                                        </td>
                                        <td>
                                            <t t-out="item['taxableAmount']"/>
                                        </td>
                                        <td>
                                            <t t-out="
                                                ('%.2f' % item['cgstRate'] if 'cgstRate' in item else 'NE') + ' + ' +
                                                ('%.2f' % item['sgstRate'] if 'sgstRate' in item else 'NE') + ' + ' +
                                                ('%.2f' % item['igstRate'] if 'igstRate' in item else 'NE') + ' + ' +
                                                ('%.2f' % item['cessRate'] if 'cessRate' in item else 'NE') + ' + ' +
                                                (str(round(item['cessNonadvol'], 2)) if 'cessNonadvol' in item else 'NE')
                                            "/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <br/>

                            <div class="border-bottom border-dark">
                                <table>
                                    <tr>
                                        <td>
                                            Total Taxable Amount <strong><t t-out="generate_json['totalValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            CGST Amount <strong><t t-out="generate_json['cgstValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            SGST Amount <strong><t t-out="generate_json['sgstValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            IGST Amount <strong><t t-out="generate_json['igstValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            CESS Amount <strong><t t-out="generate_json['cessValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            CESS Non.Advol Amt <strong><t t-out="generate_json['cessNonAdvolValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            Other Amt <strong><t t-out="generate_json['otherValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                        <td>
                                            Total Inv. Amt <strong><t t-out="generate_json['totInvValue']" t-options='{"widget": "monetary", "display_currency": doc.company_currency_id}'/></strong>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <br/>

                            <h5>Transportation Details</h5>
                            <table class="border-bottom border-dark">
                                <tr>
                                    <td>Transporter ID and Name: <t t-out="doc.transporter_id.vat"/> &amp;
                                        <t t-out="doc.transporter_id.name"/>
                                    </td>
                                    <td>Transporter Doc and Date: <t t-out="doc.transportation_doc_no"/> &amp;
                                        <t t-out="doc.transportation_doc_date"/>
                                    </td>
                                </tr>
                            </table>
                            <br/>

                            <h5>Vehicle Details</h5>
                            <div class="border border-dark">
                                <table>
                                    <thead>
                                        <tr class="border-bottom border-dark">
                                            <th>Mode</th>
                                            <th>Vehicle / Trans <br/>Doc No &amp; Dt.</th>
                                            <th>From</th>
                                            <t t-if="doc.state not in challan_states">
                                                <th>Entered Date</th>
                                                <th>Entered By</th>
                                            </t>
                                            <t t-else=""><th>Approx Distance</th></t>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <t t-out="dict(doc._fields['mode']._description_selection
                                                (doc.env)).get(doc.mode, '')"/>
                                            </td>
                                            <td>
                                                <t t-if="doc.vehicle_no" t-out="doc.vehicle_no"/>
                                                <t t-else="" t-out="doc.transportation_doc_no"/><t t-out="doc.transportation_doc_date"/>
                                            </td>
                                            <td>
                                                <t t-out="generate_json['fromPlace']"/>
                                            </td>
                                            <t t-if="doc.state not in challan_states">
                                                <td>
                                                    <t t-out="doc.ewaybill_date"/>
                                                </td>
                                                <td>
                                                    <t t-out="doc.company_id.vat"/>
                                                </td>
                                            </t>
                                            <t t-else="">
                                                <td>
                                                    <t t-out="doc.distance"/>Km
                                                </td>
                                            </t>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <br/>

                            <div class="border-bottom border-dark text-center">
                                <t t-if="doc.state not in challan_states" >
                                    <img t-if="doc.name != False" t-att-src="'/report/barcode/Code128/' + doc.name" style="width:250px;height:35px;" alt="Barcode"/>
                                    <br/>
                                    <t t-out="doc.name"/>
                                </t>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>

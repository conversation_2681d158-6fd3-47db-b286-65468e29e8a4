# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# <PERSON>, 2024
# da<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Ha Ketem <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid ""
"An error occurred during processing of your payment (code %s). Please try "
"again."
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_provider__code__buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__buckaroo_secret_key
msgid "Buckaroo Secret Key"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__code
msgid "Code"
msgstr "קוד"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "לא נמצאה עסקה המתאימה למספר האסמכתא %s."

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "עסקת תשלום"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Received data with missing transaction keys"
msgstr ""

#. module: payment_buckaroo
#: model_terms:ir.ui.view,arch_db:payment_buckaroo.payment_provider_form
msgid "Secret Key"
msgstr "מפתח סודי"

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_provider__buckaroo_website_key
msgid "The key solely used to identify the website with Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Unknown status code: %s"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__buckaroo_website_key
msgid "Website Key"
msgstr ""

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Your payment was refused (code %s). Please try again."
msgstr ""

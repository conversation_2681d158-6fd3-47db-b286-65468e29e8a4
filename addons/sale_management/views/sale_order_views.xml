<?xml version="1.0"?>
<odoo>

    <record id="sale_order_form_quote" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.sale_management</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <page name="order_lines" position="after">
                <page string="Optional Products"
                    name="optional_products"
                    invisible="state not in ['draft', 'sent']">
                    <field name="sale_order_option_ids" mode="list,kanban" readonly="state in ['cancel', 'sale']">
                        <form string="Optional Products">
                            <group>
                                <field name="product_id"
                                    domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                                <field name="name"/>
                                <field name="quantity"/>
                                <field name="product_uom_category_id" invisible="1"/>
                                <field name="uom_id" groups="uom.group_uom"/>
                                <field name="price_unit"/>
                                <field name="discount" groups="sale.group_discount_per_so_line"/>
                                <field name="is_present" />
                            </group>
                        </form>
                        <kanban class="o_kanban_mobile">
                            <field name="is_present" />
                            <templates>
                                <t t-name="card">
                                    <div class="row">
                                        <field name="product_id" class="col-10 fw-bolder"/>
                                        <button name="button_add_to_order"
                                            class="col-2 btn btn-link oe_link fa fa-shopping-cart"
                                            title="Add to order lines"
                                            type="object"
                                            invisible="is_present"/>
                                    </div>
                                    <div class="text-muted">
                                        Quantity:
                                        <field name="quantity"/>
                                        <field name="uom_id" groups="uom.group_uom"/>
                                    </div>
                                    <div class="text-muted">
                                        Unit Price:
                                        <field name="price_unit"/>
                                    </div>
                                </t>
                            </templates>
                        </kanban>
                        <list string="Sales Quotation Template Lines"
                            editable="bottom"
                            decoration-success="is_present == True">
                            <control>
                                <create name="add_product_control" string="Add a product"/>
                            </control>
                            <field name="sequence" widget="handle"/>
                            <field name="product_id"
                                domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"/>
                            <field name="name" optional="show"/>
                            <field name="quantity"/>
                            <field name="uom_id" string="UoM" groups="uom.group_uom" optional="show"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="price_unit"/>
                            <field name="discount"
                                width="50px"
                                string="Disc.%"
                                groups="sale.group_discount_per_so_line"
                                optional="show"/>
                            <field name="is_present" column_invisible="True" />
                            <button name="button_add_to_order"
                                type="object"
                                class="oe_link"
                                icon="fa-shopping-cart"
                                title="Add to order lines"
                                invisible="is_present"/>
                        </list>
                    </field>
                </page>
            </page>

            <field name="partner_shipping_id" position="after">
                <field name="sale_order_template_id"
                    options="{'no_create': True}"
                    groups="sale_management.group_sale_order_template"
                 readonly="state in ['cancel', 'sale']"/>
            </field>
        </field>
    </record>

</odoo>

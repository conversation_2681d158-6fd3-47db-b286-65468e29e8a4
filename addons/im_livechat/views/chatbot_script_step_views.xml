<?xml version="1.0" encoding="UTF-8" ?>
<odoo><data>

    <record id="chatbot_script_step_view_form" model="ir.ui.view">
        <field name="name">chatbot.script.step.view.form</field>
        <field name="model">chatbot.script.step</field>
        <field name="arch" type="xml">
            <form disable_autofocus="1">
                <field name="is_forward_operator_child" invisible="1"/>
                <div class="alert alert-info text-center mb-0" role="alert" invisible="not is_forward_operator_child">
                    <span>Reminder: This step will only be played if no operator is available.</span>
                </div>
                <div class="alert alert-info text-center mb-0" role="alert" invisible="step_type != 'forward_operator'">
                    <span>Tip: Plan further steps for the Bot in case no operator is available.</span>
                </div>
                <sheet>
                    <group>
                        <group>
                            <field name="sequence" invisible="1"/>
                            <field name="message" widget="text_emojis" placeholder="e.g. 'How can I help you?'"
                                required="step_type != 'forward_operator'"/>
                            <field name="chatbot_script_id" invisible="1"/>
                            <field name="step_type"/>
                            <field name="triggering_answer_ids" widget="chatbot_triggering_answers_widget"
                                    options="{'no_create': True}">
                                <list>
                                    <!-- added only to correctly fetch the display_name for the tag display -->
                                    <field name="display_name" column_invisible="True"/>
                                </list>
                            </field>
                        </group>
                        <group>
                            <field name="answer_ids" invisible="step_type != 'question_selection'" nolabel="1">
                                <list editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="display_name" column_invisible="True"/>
                                    <field name="name"/>
                                    <field name="redirect_link" string="Optional Link"/>
                                </list>
                            </field>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="chatbot_script_step_view_tree" model="ir.ui.view">
        <field name="name">chatbot.script.step.view.list</field>
        <field name="model">chatbot.script.step</field>
        <field name="arch" type="xml">
            <list default_order="sequence asc">
                <field name="sequence" widget="handle"/>
                <field name="message"/>
                <field name="step_type"/>
                <field name="answer_ids" widget="many2many_tags"/>
                <field name="triggering_answer_ids" widget="many2many_tags"/>
            </list>
        </field>
    </record>

</data></odoo>

# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* utm
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_source.py:0
msgid "%(content)s (%(model_description)s created on %(create_date)s)"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__active
#: model:ir.model.fields,field_description:utm.field_utm_medium__active
msgid "Active"
msgstr "Aktivno"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Approval-based Flow"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Approved"
msgstr "Odobreno"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Archive"
msgstr "Arhiv"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Archived"
msgstr "Arhivirano"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Assign tags to your campaigns to organize, filter and track them."
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Audience-driven Flow"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr "Samodejno ustvarjena kampanja"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__campaign_id
msgid "Campaign"
msgstr "Kampanja"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__name
msgid "Campaign Identifier"
msgstr "Identifikacijska oznaka kampanje"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__title
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "Campaign Name"
msgstr "Naziv kampanje"

#. module: utm
#: model:ir.model,name:utm.model_utm_stage
msgid "Campaign Stage"
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_tag
#: model_terms:ir.ui.view,arch_db:utm.utm_tag_view_tree
msgid "Campaign Tags"
msgstr "Oznake kampanje"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_campaign_action
#: model:ir.ui.menu,name:utm.menu_utm_campaign_act
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns"
msgstr "Kampanje"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid ""
"Campaigns are used to centralize your marketing efforts and track their "
"results."
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Campaigns that are assigned to me"
msgstr ""

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_christmas_special
msgid "Christmas Special"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Collect ideas, design creative content and publish it once reviewed."
msgstr ""
"Zbirajte ideje, oblikujte kreativno vsebino in jo po pregledu objavite."

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__color
#: model:ir.model.fields,field_description:utm.field_utm_tag__color
msgid "Color Index"
msgstr "Barvni indeks"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Copywriting"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid "Create a Medium"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_tag
msgid "Create a Tag"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_campaign_action
msgid "Create a campaign"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid "Create a stage for your campaigns"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__create_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__create_date
#: model:ir.model.fields,field_description:utm.field_utm_source__create_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__create_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Creative Flow"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Delete"
msgstr "Izbriši"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Deploy"
msgstr "Namestitev"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Deployed"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_2
msgid "Design"
msgstr "Načrtovanje"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__display_name
#: model:ir.model.fields,field_description:utm.field_utm_medium__display_name
#: model:ir.model.fields,field_description:utm.field_utm_source__display_name
#: model:ir.model.fields,field_description:utm.field_utm_stage__display_name
#: model:ir.model.fields,field_description:utm.field_utm_tag__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Done"
msgstr "Opravljeno"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Edit"
msgstr "Uredi"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_email_campaign_products
msgid "Email Campaign - Products"
msgstr "Kampanje elektronske pošte - proizvodi"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_email_campaign_services
msgid "Email Campaign - Services"
msgstr "Storitve kampanje preko elektronske pošte"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Event-driven Flow"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Gather Data"
msgstr "Zbiranje podatkov"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid ""
"Gather data, build a recipient list and write content based on your "
"Marketing target."
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Group By"
msgstr "Združi po"

#. module: utm
#: model:ir.model,name:utm.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP usmerjanje"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__id
#: model:ir.model.fields,field_description:utm.field_utm_medium__id
#: model:ir.model.fields,field_description:utm.field_utm_source__id
#: model:ir.model.fields,field_description:utm.field_utm_stage__id
#: model:ir.model.fields,field_description:utm.field_utm_tag__id
msgid "ID"
msgstr "ID"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Ideas"
msgstr "Ideas"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_source__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_uid
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__write_date
#: model:ir.model.fields,field_description:utm.field_utm_medium__write_date
#: model:ir.model.fields,field_description:utm.field_utm_source__write_date
#: model:ir.model.fields,field_description:utm.field_utm_stage__write_date
#: model:ir.model.fields,field_description:utm.field_utm_tag__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Later"
msgstr "Pozneje"

#. module: utm
#: model:ir.ui.menu,name:utm.menu_link_tracker_root
msgid "Link Tracker"
msgstr "Sledilnik povezavam"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "List-Building"
msgstr "Oblikovanje seznama"

#. module: utm
#: model:utm.tag,name:utm.utm_tag_1
msgid "Marketing"
msgstr "Marketing"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__medium_id
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_form
msgid "Medium"
msgstr "Medij"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_medium__name
msgid "Medium Name"
msgstr "Naziv medija"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_medium_action
#: model:ir.ui.menu,name:utm.menu_utm_medium
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "Mediums"
msgstr "Mediji"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "My Campaigns"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source_mixin__name
#: model:ir.model.fields,field_description:utm.field_utm_stage__name
#: model:ir.model.fields,field_description:utm.field_utm_tag__name
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
msgid "Name"
msgstr "Naziv"

#. module: utm
#: model:utm.stage,name:utm.default_utm_stage
msgid "New"
msgstr "Novo"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid "No Sources yet!"
msgstr ""

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_medium.py:0
msgid ""
"Oops, you can't delete the Medium '%s'.\n"
"Doing so would be like tearing down a load-bearing wall — not the best idea."
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Pre-Launch"
msgstr "Pred zagonom"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Prepare Campaigns and get them approved before making them go live."
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid ""
"Prepare your Campaign, test it with part of your audience and deploy it "
"fully afterwards."
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Report"
msgstr "Poročilo"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__user_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Responsible"
msgstr "Odgovoren"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_kanban
msgid "Restore"
msgstr "Obnovi"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Review"
msgstr "Pregled"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Running"
msgstr "V teku"

#. module: utm
#: model:utm.campaign,title:utm.utm_campaign_fall_drive
msgid "Sale"
msgstr "Prodaja"

#. module: utm
#: model:utm.stage,name:utm.campaign_stage_1
msgid "Schedule"
msgstr "Razpored"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_search
msgid "Search UTM Medium"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Send"
msgstr "Pošlji"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
#: model:utm.stage,name:utm.campaign_stage_3
msgid "Sent"
msgstr "Poslano"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_stage__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Soft-Launch"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Soft-Launch Flow"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_mixin__source_id
#: model:ir.model.fields,field_description:utm.field_utm_source_mixin__source_id
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "Source"
msgstr "Vir"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_source__name
msgid "Source Name"
msgstr "Ime vira"

#. module: utm
#: model:ir.actions.act_window,name:utm.utm_source_action
#: model:ir.ui.menu,name:utm.menu_utm_source
msgid "Sources"
msgstr "Izvori"

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__stage_id
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Stage"
msgstr "Stopnja"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_search
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_tree
msgid "Stages"
msgstr "Stopnje"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.action_view_utm_stage
msgid ""
"Stages allow you to organize your workflow  (e.g. plan, design, in progress,"
" done, …)."
msgstr ""

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_tag__color
msgid ""
"Tag color. No color means no display in kanban to distinguish internal tags "
"from public categorization tags."
msgstr ""

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_tag_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: utm
#: model:ir.model.fields,field_description:utm.field_utm_campaign__tag_ids
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "Tags"
msgstr "Ključne besede"

#. module: utm
#: model:ir.model.constraint,message:utm.constraint_utm_campaign_unique_name
#: model:ir.model.constraint,message:utm.constraint_utm_medium_unique_name
#: model:ir.model.constraint,message:utm.constraint_utm_source_unique_name
msgid "The name must be unique"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "This Month"
msgstr "Ta mesec"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "This Week"
msgstr "Ta teden"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Naziv, ki pomaga slediti različnim kampanjam, Primer: Posebna_ponudba, "
"Božična_akcija"

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "Metoda dostave. Primer: razglednica, E-pošta, pasica, ..."

#. module: utm
#: model:ir.model.fields,help:utm.field_utm_mixin__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Vir povezave. Primer: Iskalnik, druga domena, naziv e-poštnega seznama"

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "To be Approved"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid ""
"Track incoming events (e.g. Christmas, Black Friday, ...) and publish timely"
" content."
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_campaign
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
msgid "UTM Campaign"
msgstr "UTM kampanja"

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:utm.view_utm_campaign_view_search
msgid "UTM Campaigns"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_medium
msgid "UTM Medium"
msgstr "UTM Medij"

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_medium_action
msgid ""
"UTM Mediums track the mean that was used to attract traffic (e.g. "
"\"Website\", \"X\", ...)."
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_mixin
msgid "UTM Mixin"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_source
msgid "UTM Source"
msgstr "UTM vir"

#. module: utm
#: model:ir.model,name:utm.model_utm_source_mixin
msgid "UTM Source Mixin"
msgstr ""

#. module: utm
#: model_terms:ir.actions.act_window,help:utm.utm_source_action
msgid ""
"UTM Sources track where traffic comes from  (e.g. \"May Newsletter\", \"\", "
"...)."
msgstr ""

#. module: utm
#: model:ir.actions.act_window,name:utm.action_view_utm_stage
msgid "UTM Stages"
msgstr ""

#. module: utm
#: model:ir.model,name:utm.model_utm_tag
msgid "UTM Tag"
msgstr ""

#. module: utm
#: model:ir.ui.menu,name:utm.marketing_utm
msgid "UTMs"
msgstr ""

#. module: utm
#. odoo-javascript
#: code:addons/utm/static/src/js/utm_campaign_kanban_examples.js:0
msgid "Use This For My Campaigns"
msgstr "Uporabite to za kampanje"

#. module: utm
#. odoo-python
#: code:addons/utm/models/utm_source.py:0
msgid ""
"You cannot update multiple records with the same name. The name should be "
"unique!"
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_stage_view_form
msgid "e.g. \"Brainstorming\""
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_source_view_tree
msgid "e.g. \"Christmas Mailing\""
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_medium_view_tree
msgid "e.g. \"Email\""
msgstr ""

#. module: utm
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:utm.utm_campaign_view_form_quick_create
msgid "e.g. Black Friday"
msgstr ""

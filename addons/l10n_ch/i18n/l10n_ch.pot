# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ch
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-12 12:18+0000\n"
"PO-Revision-Date: 2023-12-12 12:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "%s invoices could be printed in the %s format."
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_header_template
msgid "&amp;nbsp;"
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,print_report_name:l10n_ch.l10n_ch_qr_report
msgid "'QR-bill-%s' % object.name"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_200
msgid ""
"200 - Total amount of agreed or collected consideration incl. from supplies "
"opted for taxation, transfer of supplies acc. to the notification procedure "
"and supplies provided abroad (worldwide turnover)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_205
msgid ""
"205 - Consideration reported in Ref. 200 from supplies exempt from the tax "
"without credit (art. 21) where the option for their taxation according to "
"art. 22 has been exercised"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_220_289
msgid ""
"220 - Supplies exempt from the tax (e.g. export, art. 23) and supplies "
"provided to institutional and individual beneficiaries that are exempt from "
"liability for tax (art. 107 para. 1 lit. a)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_221
msgid "221 - Supplies provided abroad"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_225
msgid ""
"225 - Transfer of supplies according to the notification procedure (art. 38,"
" please submit Form 764)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_230
msgid ""
"230 - Supplies provided on Swiss territory exempt from the tax without "
"credit (art. 21) and where the option for their taxation according to art. "
"22 has not been exercised"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_235
msgid "235 - Reduction of consideration (discounts, rebates etc.)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_280
msgid ""
"280 - Miscellaneous (e.g. land value, purchase prices in case of margin "
"taxation)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_289
msgid "289 - Deductions (Total Ref. 220 to 280)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_299
msgid "299 - Taxable turnover (Ref. 200 minus Ref. 289)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_302a
msgid "302a - Standard rate (7,7%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_302b
msgid "302b - Standard rate (7,7%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_303a
msgid "303a - Standard rate (8,1%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_303b
msgid "303b - Standard rate (8,1%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_312a
msgid "312a - Reduced rate (2,5%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_312b
msgid "312b - Reduced rate (2,5%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_313a
msgid "313a - Reduced rate (2,6%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_313b
msgid "313b - Reduced rate (2,6%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_342a
msgid "342a - Accommodation rate (3,7%): Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_342b
msgid "342b - Accommodation rate (3,7%): Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_343a
msgid "343a - Accommodation rate (3,8%): Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_343b
msgid ""
"343b - Accommodation rate (3,8%): Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_382a
msgid "382a - Acquisition tax: Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_382b
msgid "382b - Acquisition tax: Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_383a
msgid "383a - Acquisition tax: Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_383b
msgid "383b - Acquisition tax: Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_399
msgid "399 - Total amount of tax due"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_400
msgid "400 - Input tax on cost of materials and supplies of services"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_405
msgid "405 - Input tax on investments and other operating costs"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_410
msgid "410 - De-taxation (art. 32)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_415
msgid ""
"415 - Correction of the input tax deduction: mixed use (art. 30), own use "
"(art. 31)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_420
msgid ""
"420 - Reduction of the input tax deduction: Flow of funds, which are not "
"deemed to be consideration, such as subsidies, tourist charges (art. 33 "
"para. 2)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_479
msgid "479 - Total Ref. 400 to 420"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_500
msgid "500 - Amount payable"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_510
msgid "510 - Credit in favour of the taxable person"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_900
msgid ""
"900 - Subsidies, tourist funds collected by tourist offices, contributions "
"from cantonal water, sewage or waste funds (art. 18 para. 2 lit. a to c)"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_910
msgid ""
"910 - Donations, dividends, payments of damages etc. (art. 18 para. 2 lit. d"
" to l)"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span class=\"title\">Acceptance point</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span class=\"title\">Reference</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Account / Payable to</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Account / Payable to</span><br/>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Additional information</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Amount</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Amount</span><br/>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Currency</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Payable by</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Payment part</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Receipt</span>"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_swissqr_template
msgid "<span>Reference</span>"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "All selected invoices must belong to the same Switzerland company"
msgstr ""

#. module: l10n_ch
#: model:account.report.column,name:l10n_ch.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_qr_invoice_wizard_form
msgid "Check invalid invoices"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__classic_inv_text
msgid "Classic Inv Text"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_bank_statement_line__l10n_ch_is_qr_valid
#: model:ir.model.fields,help:l10n_ch.field_account_move__l10n_ch_is_qr_valid
#: model:ir.model.fields,help:l10n_ch.field_account_payment__l10n_ch_is_qr_valid
msgid "Determines whether an invoice can be printed as a QR or not"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_l10n_ch_qr_invoice_wizard
msgid "Handles problems occurring while creating multiple QR-invoices at once"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chiffre_af
msgid "I. TURNOVER"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__id
msgid "ID"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_calc_impot
msgid "II. TAX CALCULATION"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_chtax_autres_mouv
msgid "III. OTHER CASH FLOWS"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "Invalid Invoices"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_display_qr_bank_options
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_display_qr_bank_options
msgid "L10N Ch Display Qr Bank Options"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_bank_statement_line__l10n_ch_is_qr_valid
#: model:ir.model.fields,field_description:l10n_ch.field_account_move__l10n_ch_is_qr_valid
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_is_qr_valid
msgid "L10N Ch Is Qr Valid"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_payment__l10n_ch_reference_warning_msg
msgid "L10N Ch Reference Warning Msg"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__nb_classic_inv
msgid "Nb Classic Inv"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__nb_qr_inv
msgid "Nb Qr Inv"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "No invoice could be printed in the %s format."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "No invoice was found to be printed."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/wizard/qr_invoice_wizard.py:0
msgid "One invoice could be printed in the %s format."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/account_invoice.py:0
msgid "Only customers invoices can be QR-printed."
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/account_payment.py:0
msgid ""
"Please fill in a correct QRR reference in the payment reference. The banks "
"will refuse your payment file otherwise."
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_qr_invoice_wizard_form
msgid "Print All"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_qr_iban
#: model:ir.model.fields,help:l10n_ch.field_res_partner_bank__l10n_ch_qr_iban
msgid ""
"Put the QR-IBAN here for your own bank accounts.  That way, you can still "
"use the main IBAN in the Account Number while you will see the QR-IBAN for "
"the barcode.  "
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_qr_invoice_wizard_form
msgid "QR printing encountered a problem"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_account_setup_bank_manual_config__l10n_ch_qr_iban
#: model:ir.model.fields,field_description:l10n_ch.field_res_partner_bank__l10n_ch_qr_iban
msgid "QR-IBAN"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "QR-IBAN %r is invalid."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "QR-IBAN numbers are only available in Switzerland."
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,name:l10n_ch.l10n_ch_qr_report
msgid "QR-bill"
msgstr ""

#. module: l10n_ch
#: model:ir.actions.report,name:l10n_ch.l10n_ch_qr_header
msgid "QR-bill Header"
msgstr ""

#. module: l10n_ch
#: model:ir.actions.act_window,name:l10n_ch.l10n_ch_qr_invoice_wizard
msgid "Qr Batch error Wizard"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,field_description:l10n_ch.field_l10n_ch_qr_invoice_wizard__qr_inv_text
msgid "Qr Inv Text"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/account_invoice.py:0
msgid "Some invoices could not be printed in the QR format"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_supplies_1
msgid "Supplies CHF from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_supplies_2
msgid "Supplies CHF to 31.12.2023"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "Swiss QR bill"
msgstr ""

#. module: l10n_ch
#: model:ir.model,name:l10n_ch.model_report_l10n_ch_qr_report_main
msgid "Swiss QR-bill report"
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields.selection,name:l10n_ch.selection__account_journal__invoice_reference_model__ch
msgid "Switzerland"
msgstr ""

#. module: l10n_ch
#: model:account.report,name:l10n_ch.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_tax_amount_1
msgid "Tax amount CHF / cent. from 01.01.2024"
msgstr ""

#. module: l10n_ch
#: model:account.report.line,name:l10n_ch.account_tax_report_line_tax_amount_2
msgid "Tax amount CHF / cent. to 31.12.2023"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "The Swiss QR code could not be generated for the following reason(s):"
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "The account type isn't QR-IBAN or IBAN."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "The currency isn't EUR nor CHF."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid "The debtor partner's address isn't located in Switzerland."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid ""
"The partner must have a complete postal address (street, zip, city and "
"country)."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid ""
"The partner set on the bank account meant to receive the payment (%s) must "
"have a complete postal address (street, zip, city and country)."
msgstr ""

#. module: l10n_ch
#: model_terms:ir.ui.view,arch_db:l10n_ch.l10n_ch_qr_invoice_wizard_form
msgid ""
"To be able to print all invoices in the QR format, you might need to : <br/>\n"
"                    - check the account is a valid QR-IBAN<br/>\n"
"                    - or check your company and the partners are located in Switzerland.<br/>\n"
"                    Press Check Invalid Invoices to see a list of the invoices that were printed without a QR."
msgstr ""

#. module: l10n_ch
#. odoo-python
#: code:addons/l10n_ch/models/res_bank.py:0
msgid ""
"When using a QR-IBAN as the destination account of a QR-code, the payment "
"reference must be a QR-reference."
msgstr ""

#. module: l10n_ch
#: model:ir.model.fields,help:l10n_ch.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""

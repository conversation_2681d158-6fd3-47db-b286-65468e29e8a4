# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_membership
#
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:56+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External link\" title=\"External link\"/>"
msgstr "<span class=\"fa fa-external-link\" role=\"img\" aria-label=\"External link\" title=\"External link\"/>"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "All"
msgstr "همه"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
msgid "All Countries"
msgstr "تمام کشورها"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Associations"
msgstr "انجمنها"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
msgid "Close"
msgstr "بستن"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Find a business partner"
msgstr "یافتن همکار تجاری"

#. module: website_membership
#: code:addons/website_membership/controllers/main.py:0
msgid "Free Members"
msgstr "اعضای آزاد"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_country
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Location"
msgstr "مکان"

#. module: website_membership
#: code:addons/website_membership/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Members"
msgstr "اعضا"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "Members Page"
msgstr ""

#. module: website_membership
#: model:ir.model,name:website_membership.model_membership_membership_line
msgid "Membership Line"
msgstr "خط عضویت"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "No result found."
msgstr "نتیجه‌ای یافت نشد."

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.index
msgid "Our Members Directory"
msgstr "دایرکتوری اعضاء ما"

#. module: website_membership
#: model:ir.model,name:website_membership.model_website
msgid "Website"
msgstr "تارنما"

#. module: website_membership
#: model_terms:ir.ui.view,arch_db:website_membership.opt_index_google_map
#: model_terms:ir.ui.view,arch_db:website_membership.snippet_options
msgid "World Map"
msgstr "نقشه جهان"

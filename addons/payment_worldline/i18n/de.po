# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_worldline
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Key"
msgstr "API-Schlüssel"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "API Secret"
msgstr "API-Geheimnis"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__code
msgid "Code"
msgstr "Code"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Verbindung mit API konnte nicht hergestellt werden."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Keine Transaktion gefunden, die der Referenz %s entspricht."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "PSPID"
msgstr "PSPID"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_provider
msgid "Payment Provider"
msgstr "Zahlungsanbieter"

#. module: payment_worldline
#: model:ir.model,name:payment_worldline.model_payment_transaction
msgid "Payment Transaction"
msgstr "Zahlungstransaktion"

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "Erhaltene Daten mit fehlendem Zahlungsstatus."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received data with missing reference %(ref)s."
msgstr "Erhaltene Daten mit fehlender Referenz %(ref)s."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "Received invalid transaction status %(status)s."
msgstr "Ungültiger Transaktionsstatus %(status)s erhalten."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr "Die Kommunikation mit der API ist fehlgeschlagen. Details: %s"

#. module: payment_worldline
#: model:ir.model.fields,help:payment_worldline.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Der technische Code dieses Zahlungsanbieters."

#. module: payment_worldline
#. odoo-python
#: code:addons/payment_worldline/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "Die Transaktion ist nicht mit einem Token verknüpft."

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Key"
msgstr "Webhook-Schlüssel"

#. module: payment_worldline
#: model_terms:ir.ui.view,arch_db:payment_worldline.payment_provider_form
msgid "Webhook Secret"
msgstr "Webhook-Geheimnis"

#. module: payment_worldline
#: model:ir.model.fields.selection,name:payment_worldline.selection__payment_provider__code__worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_key
msgid "Worldline API Key"
msgstr "API-Schlüssel von Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_api_secret
msgid "Worldline API Secret"
msgstr "API-Geheimnis von Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_pspid
msgid "Worldline PSPID"
msgstr "PSPID von Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_key
msgid "Worldline Webhook Key"
msgstr "Webhook-Schlüssel von Worldline"

#. module: payment_worldline
#: model:ir.model.fields,field_description:payment_worldline.field_payment_provider__worldline_webhook_secret
msgid "Worldline Webhook Secret"
msgstr "Webhook-Geheimnis von Worldline"

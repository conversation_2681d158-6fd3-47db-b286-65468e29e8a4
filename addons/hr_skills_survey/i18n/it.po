# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_survey
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_valid
msgid "AWS Cloud"
msgstr "CLoud AWS"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__survey_id
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__display_type__certification
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Certification"
msgstr "Certificazione"

#. module: hr_skills_survey
#: model:ir.ui.menu,name:hr_skills_survey.hr_employee_certication_report_menu
msgid "Certifications"
msgstr "Certificazioni"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Department"
msgstr "Ufficio"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Tipo visualizzato"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Employee"
msgstr "Dipendente"

#. module: hr_skills_survey
#: model:ir.actions.act_window,name:hr_skills_survey.hr_employee_certification_report_action
msgid "Employee Certifications"
msgstr "Certificazioni dipendente"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__expiration_status
msgid "Expiration Status"
msgstr "Stato scadenza"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiration date"
msgstr "Data di scadenza"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expired
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expired"
msgstr "Scaduto"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expiring
msgid "Expiring"
msgstr "In scadenza"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiring Soon"
msgstr "Scadenza a breve"

#. module: hr_skills_survey
#: model:hr.resume.line.type,name:hr_skills_survey.resume_type_certification
msgid "Internal Certification"
msgstr "Certificazione interna"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_expiring
msgid "MongoDB Developer"
msgstr "Sviluppatore MongoDB"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_aws
msgid "Oracle DB"
msgstr "DB Oracle"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Riga CV di un dipendente"

#. module: hr_skills_survey
#: model:ir.model.fields,help:hr_skills_survey.field_survey_survey__certification_validity_months
msgid ""
"Specify the number of months the certification is valid after being awarded."
" Enter 0 for certifications that never expire."
msgstr ""
"Specifica il numero di mesi di validità della certificazione dopo averla "
"ottenuta. Scrivi 0 per le certificazioni senza scadenza."

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_survey
msgid "Survey"
msgstr "Sondaggio"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Input degli utenti del sondaggio"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__valid
msgid "Valid"
msgstr "Valido"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Valid Until"
msgstr "Valido fino al"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_survey_survey__certification_validity_months
msgid "Validity"
msgstr "Validità"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity End"
msgstr "Fine validità"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity Start"
msgstr "Inizio Validità"

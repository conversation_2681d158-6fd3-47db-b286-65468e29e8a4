# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_survey
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Bo<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_valid
msgid "AWS Cloud"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__survey_id
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__display_type__certification
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Certification"
msgstr "Certificiranje"

#. module: hr_skills_survey
#: model:ir.ui.menu,name:hr_skills_survey.hr_employee_certication_report_menu
msgid "Certifications"
msgstr "Certifikacije"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Department"
msgstr "Odjel"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Tip prikaza"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Employee"
msgstr "Djelatnik"

#. module: hr_skills_survey
#: model:ir.actions.act_window,name:hr_skills_survey.hr_employee_certification_report_action
msgid "Employee Certifications"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__expiration_status
msgid "Expiration Status"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiration date"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expired
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expired"
msgstr "Isteklo"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expiring
msgid "Expiring"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiring Soon"
msgstr "Uskoro ističe"

#. module: hr_skills_survey
#: model:hr.resume.line.type,name:hr_skills_survey.resume_type_certification
msgid "Internal Certification"
msgstr ""

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_expiring
msgid "MongoDB Developer"
msgstr ""

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_aws
msgid "Oracle DB"
msgstr ""

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Stavka životopisa djelatnika"

#. module: hr_skills_survey
#: model:ir.model.fields,help:hr_skills_survey.field_survey_survey__certification_validity_months
msgid ""
"Specify the number of months the certification is valid after being awarded."
" Enter 0 for certifications that never expire."
msgstr ""

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_survey
msgid "Survey"
msgstr "Anketa"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "Anketa koju popunjava korisnik"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__valid
msgid "Valid"
msgstr "Odobreno"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Valid Until"
msgstr "Vrijedi do"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_survey_survey__certification_validity_months
msgid "Validity"
msgstr "Valjanost"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity End"
msgstr ""

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity Start"
msgstr "Valjanost počinje"

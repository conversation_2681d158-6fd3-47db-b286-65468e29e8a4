# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bus
# 
# Translators:
# <PERSON>il <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: bus
#: model:ir.model,name:bus.model_res_groups
msgid "Access Groups"
msgstr "Gruppi di accesso"

#. module: bus
#: model:ir.model,name:bus.model_ir_attachment
msgid "Attachment"
msgstr "Allegato"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__away
msgid "Away"
msgstr "Assente"

#. module: bus
#: model:ir.model,name:bus.model_bus_listener_mixin
msgid "Can send messages via bus.bus"
msgstr "Può inviare messaggi via bus.bus"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__channel
msgid "Channel"
msgstr "Canale"

#. module: bus
#: model:ir.model,name:bus.model_bus_bus
msgid "Communication Bus"
msgstr "Bus di comunicazione"

#. module: bus
#: model:ir.model,name:bus.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__display_name
#: model:ir.model.fields,field_description:bus.field_bus_presence__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: bus
#: model:ir.model,name:bus.model_ir_http
msgid "HTTP Routing"
msgstr "Routing HTTP"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__id
#: model:ir.model.fields,field_description:bus.field_bus_presence__id
msgid "ID"
msgstr "ID"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__status
#: model:ir.model.fields,field_description:bus.field_res_partner__im_status
#: model:ir.model.fields,field_description:bus.field_res_users__im_status
msgid "IM Status"
msgstr "Stato IM"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_poll
msgid "Last Poll"
msgstr "Ultimo poll"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__last_presence
msgid "Last Presence"
msgstr "Ultima presenza"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_bus__message
msgid "Message"
msgstr "Messaggio"

#. module: bus
#: model:ir.model,name:bus.model_ir_model
msgid "Models"
msgstr "Modelli"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__offline
msgid "Offline"
msgstr "Fuori linea"

#. module: bus
#: model:ir.model.fields.selection,name:bus.selection__bus_presence__status__online
msgid "Online"
msgstr "In linea"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "Refresh"
msgstr "Ricarica"

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid ""
"Save your work and refresh to get the latest updates and avoid potential "
"issues."
msgstr ""
"Salva il tuo lavoro e ricarica la pagina per ottenere gli ultimi "
"aggiornamenti ed evitare problemi."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/assets_watchdog_service.js:0
msgid "The page appears to be out of date."
msgstr "La pagina non sembra essere aggiornata."

#. module: bus
#. odoo-javascript
#: code:addons/bus/static/src/services/bus_service.js:0
msgid "The page is out of date"
msgstr "La pagina non è aggiornata"

#. module: bus
#: model:ir.model,name:bus.model_res_users
msgid "User"
msgstr "Utente"

#. module: bus
#: model:ir.model,name:bus.model_bus_presence
msgid "User Presence"
msgstr "Presenza utente"

#. module: bus
#: model:ir.model,name:bus.model_res_users_settings
msgid "User Settings"
msgstr "Impostazioni utente"

#. module: bus
#: model:ir.model.fields,field_description:bus.field_bus_presence__user_id
msgid "Users"
msgstr "Utenti"

#. module: bus
#. odoo-python
#: code:addons/bus/controllers/home.py:0
msgid ""
"Your password is the default (admin)! If this system is exposed to untrusted"
" users it is important to change it immediately for security reasons. I will"
" keep nagging you about it!"
msgstr ""
"La password risulta quella predefinita (admin). Se il sistema viene esposto "
"a utenti non fidati, è essenziale cambiarla immediatamente per motivi di "
"sicurezza. Tale avviso verrà riproposto in modo insistente."

#. module: bus
#: model:ir.model,name:bus.model_ir_websocket
msgid "websocket message handling"
msgstr "Gestione dei messaggi websocket"

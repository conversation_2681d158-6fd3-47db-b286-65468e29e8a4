<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_emr_waseel_cov_request_tree" model="ir.ui.view">
        <field name="name">emr.waseel.cov.request.tree</field>
        <field name="model">emr_waseel.cov_request</field>
        <field name="arch" type="xml">
            <tree string="Waseel Batches" create="false" edit="false" default_order="upload_date asc" js_class="button_create_batch_tree">
                <header>
                    <button name="send_wsl_req" string="Send Wsl Req" class="oe_highlight" type="object"/>
                    <button name="force_send_wsl_req" string="Force Send Wsl Req" class="oe_highlight" type="object"/>
                </header>
                <field name="wsl_company"/>
                <field name="wsl_cov_company"/>
                <field name="upload_date"/>
                <field name="upload_name"/>
                <field name="upload_no"/>
                <field name="batch_type" widget="badge" decoration-success="batch_type == 'op'" decoration-info="batch_type == 'ip'" />
                <button name="action_view_details" type="object" class="oe_stat_button" icon="fa-info-circle"/>
            </tree>
        </field>
    </record>

    <record id="view_emr_waseel_cov_request_form" model="ir.ui.view">
        <field name="name">emr.waseel.cov.request.form</field>
        <field name="model">emr_waseel.cov_request</field>
        <field name="arch" type="xml">
            <form string="Waseel Batch Details">
                <sheet>
                    <group string="Waseel Upload details" >
                        <group>
                            <field name="wsl_cov_company" string="Payer" />
                            <field name="wsl_company" string="Company" />
                            <field name="upload_date" string="Upload Date" />
                        </group>
                        <group>
                            <field name="batch_type" string="Batch Type" widget="badge" decoration-success="batch_type == 'op'" decoration-info="batch_type == 'ip'" />
                            <field name="upload_name" string="Upload Name" />
                            <field name="upload_no" string="Upload No" />
                        </group>
                    </group>
                    <group string="Waseel Request details">
                        <group >
                            <field name="noOfNotUploadedClaims" string="No Of Not Uploaded Claims" />
                            <field name="noOfUploadedClaims" string="No Of Uploaded Claims" />
                            <field name="totalAmtOfUploadedClaims" string="Total Amount Of Uploaded Claims" />
                            <field name="noOfAcceptedClaims" string="No Of Accepted Claims" />
                            <field name="totalAmtOfAcceptedClaims" string="Total Amount Of Accepted Claims" />
                        </group>
                        <group >
                            <field name="noOfNotAcceptedClaims" string="No Of Not Accepted Claims" />
                            <field name="totalAmtOfNotAcceptedClaims" string="Total Amount Of Not Accepted Claims" />
                            <field name="lastModifiedDate" string="Last Modified Date" />
                            <field name="ratioOfAccepted" string="Ratio Of Accepted" />
                            <field name="ratioOfNotAccepted" string="Ratio Of Not Accepted" />
                        </group>
                    </group>
                    <group string="Waseel Request Claims">
                        <group >
                            <field name="wsl_claim" no_create = "True" no_delete = "True" string="">
                                <!-- <tree>
                                    <field name="wsl_cov_company"/>
                                    <field name="wsl_company"/> 
                                    <field name="upload_date"/>
                                    <field name="upload_name"/>
                                    <field name="upload_no"/>
                                    <button ="action_view_details" type="object" class="oe_stat_button" icon="fa-info-circle"/>
                                </tree> -->
                            </field>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

     <record id="view_emr_waseel_cov_request_search" model="ir.ui.view">
        <field name="name">emr.waseel.cov.request.search</field>
        <field name="model">emr_waseel.cov_request</field>
        <field name="arch" type="xml">
            <search>                
                <filter name="group_by_company" string="Company" domain="[]" context="{'group_by': 'wsl_company'}"/>
                <filter name="group_by_cov_company" string="Coverage Company" domain="[]" context="{'group_by': 'wsl_cov_company'}"/>
                <filter name="group_by_batch_type" string="Batch Type" domain="[]" context="{'group_by': 'batch_type'}"/>
                <field name="wsl_company"/>
                <field name="wsl_cov_company"/>
                <field name="batch_type"/>
                <field name="upload_date"/>
            </search>
        </field>
    </record>

    <record id="action_emr_waseel_cov_request" model="ir.actions.act_window">
        <field name="name">Waseel Batches</field>
        <field name="res_model">emr_waseel.cov_request</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_emr_waseel_cov_request_tree"/>
        <field name="search_view_id" ref="view_emr_waseel_cov_request_search"/>
        <field name="context">{'search_default_group_by_batch_type': 3, 'display_special_code_name': True}</field>       
        <field name="context">{'search_default_group_by_cov_company': 2, 'display_special_code_name': True}</field>       
        <field name="context">{'search_default_group_by_company': 1, 'display_special_code_name': True}</field>        
    </record>

    <record id="action_view_details" model="ir.actions.act_window">
        <field name="name">Waseel Request Details</field>
        <field name="res_model">emr_waseel.cov_request</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_emr_waseel_cov_request_form"/>
        <field name="target">new</field> <!-- This opens the view in a pop-up -->
    </record>
</odoo>

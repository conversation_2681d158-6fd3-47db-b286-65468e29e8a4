# Visitor Management System (Deployment Architecture)
## Overview
This document provides a high-level overview of the deployment architecture for Odoo Community Edition. The deployment includes two production virtual machines (VMs) for the Odoo web application and PostgreSQL database, and one optional VM for testing. The production environment integrates with external systems such as BioStar 2 (access control), LDAP/Active Directory, and an SMTP server. Below is a simplified breakdown of the setup.

---

## Deployment Overview

### Virtual Machines (VMs)

| **VM Type**               | **Purpose**                                                                 | **Key Components**                                                                                     | **Integrations**                                                                 |
|---------------------------|-----------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------|
| **Production VM 1**       | Hosts the Odoo web application (Visitor Management System).                 | - Odoo Community (Docker) <br> - Nginx Proxy Manager (Docker) <br> - pgAdmin4 (Docker, optional)       | - BioStar 2 (access control) <br> - LDAP/Active Directory <br> - SMTP Server     |
| **Production VM 2**       | Hosts the PostgreSQL database for Odoo.                                     | - PostgreSQL (installed directly)                                                                      | - Odoo Web Application                                                           |
| **Test VM (Optional)**    | Hosts a test instance of Odoo for development and testing.                  | - Odoo Community (Docker) <br> - Nginx Proxy Manager (Docker) <br> - pgAdmin4 (Docker, optional)       | - None (isolated for testing purposes)                                           |

---

## System Specifications

### Virtual Machine Requirements

| **VM Type**               | **CPU** | **RAM** | **Storage** | **Network Requirements**                                                                 |
|---------------------------|---------|---------|-------------|------------------------------------------------------------------------------------------|
| **Production VM 1**       | 8 cores | 16 GB   | 250 GB      | Static IP, open ports: 80 (HTTP), 443 (HTTPS), 22 (SSH). Access to GitHub and Docker Hub. |
| **Production VM 2**       | 8 cores | 16 GB   | 500 GB      | Static IP, open ports: 5432 (PostgreSQL), 22 (SSH).                                      |
| **Test VM (Optional)**    | 4 cores | 8 GB    | 100 GB      | Static IP, open ports: 80 (HTTP), 443 (HTTPS), 22 (SSH).                                 |

---

## Key Features and Integrations

### Production Environment
1. **Odoo Web Application (Visitor Management)**:
   - Hosted in a Docker container for easy management.
   - Includes a reverse proxy (Nginx Proxy Manager) for secure access.
   - Optional pgAdmin4 for database management.

2. **PostgreSQL Database**:
   - Installed directly on a separate VM for better performance and reliability.
   - Configured for secure remote access from the Odoo web application.

3. **Integrations**:
   - **BioStar 2**: For access control and visitor management.
   - **LDAP/Active Directory**: For centralized user authentication.
   - **SMTP Server**: For sending email notifications.

### Test Environment (Optional)
- A replica of the production environment for testing and development purposes.
- Isolated from external systems to avoid impacting production.

---

## Deployment Diagram

```mermaid
graph TD
    subgraph Production Environment
        subgraph VM1[Production VM 1 Odoo Web Application]
            direction TB
            pgAdmin4[Docker: PgAdmin4 <br> DB Management <br> Ports:5050]
            Odoo[Docker: Odoo Web <br> Visitor Management System <br> Ports:8069,7072]
            Nginx[Docker: Nginx Proxy Manager <br> Ports:80,443,81]
            Portainer[Docker: Portainer <br> Ports:9000,9443]
            
        end

        subgraph VM2[Production VM 2]
            direction TB
            PostgreSQL[PostgreSQL Database <BR> Ports: 5432]
        end

        Odoo -->|Connects to| PostgreSQL
        pgAdmin4 -->|Connects to| PostgreSQL
        
    end


    subgraph External Integrations
        BioStar2[BioStar 2 Server] -->|Access Control| Odoo
        LDAP[LDAP/Active Directory <BR> Ports: 389] -->|User Authentication or Sync| Odoo
        SMTP[SMTP Server] -->|Email Notifications| Odoo
    end

    subgraph Repositories
        GitHub[GitHub] -->|Clone or pull updates or fix | Odoo
        DockerHub[Docker Hub] -->|Pull Docker Images| Odoo
    end

    style VM1 fill:#f9f,stroke:#333,stroke-width:2px
    style VM2 fill:#bbf,stroke:#333,stroke-width:2px
    style Repositories fill:#99f,stroke:#333,stroke-width:2px

```
## MEP - Requirement Summary Actions:
  - Prepare Virtual Machines with the specifications mentioned above
  - Install the operating system (Ubuntu 22.04 LTS recommended).
  - Open integration connection between servers and ports mentioned above
  - Prepare subdomain and certificate for web portal 
  - Prepare SMTP configuration 
  - Prepare LDAP configuration 



# Visitor Management and Access Control Workflow

## Workflow Overview

This document outlines the workflow for managing visitors in a government building using a Visitor Management System (VMS) integrated with biometric access control.

---

### 1. **Employee Registers Visitor**
- The employee accesses the **Visitor Management System** via a desktop web application.
- Visitor details are entered, and the **"Register Visitor"** function is used to initiate the process.

### 2. **Employee Creates Invitation Request**
- The employee selects the **"Create Invitation Request"** option.
- Details such as visit date, time, and purpose are filled out.
- The system generates a unique **QR Code** for the visitor.

### 3. **QR Code Sent to Visitor's Email**
- The system automatically sends an email to the visitor containing the **QR Code**.
- The visitor receives the email notification and confirms their appointment.

### 4. **Visitor Arrives at Government Building**
- The visitor arrives at the building entrance.
- Security personnel greet the visitor and request their **ID** and **QR Code**.

### 5. **Security Verifies Visitor Data**
- Security scans the visitor's **QR Code** using a handheld device or terminal.
- The system validates the visitor's identity and cross-checks with the **invitation request**.

### 6. **System Confirmation and Access Provision**
- Upon successful verification, the system:
  - **Creates a visit record** automatically.
  - Sends access credentials to the **biometric devices** at gates and doors.

### 7. **Visitor Scans QR Code for Access**
- The visitor approaches a gate or door equipped with a **QR Code scanner**.
- They scan their **QR Code**:
  - If valid, the system grants access, and the gate/door opens.

### 8. **Access Denied for Unauthorized Visitors**
- If a visitor attempts to scan without prior authorization:
  - The system displays an **"Access Denied"** message.
  - The gate/door remains closed, ensuring security.

---

## Key Benefits
- **Streamlined Visitor Experience:** Automated email notifications and QR code-based access.
- **Enhanced Security:** ID verification and system-controlled access.
- **Seamless Integration:** Biometric devices work in sync with the visitor management system.

## Diagram 
```mermaid
sequenceDiagram
    Employee ->> VMS: Register Visitor
    Employee ->> VMS: Create Invitation Request
    VMS ->> Visitor Email: Send QR Code
    Visitor ->> Security: Presents ID and QR Code
    Security ->> VMS: Verify QR Code and ID
    VMS ->> Security: Confirm Visit
    VMS ->> Biometric Device: Grant Access
    Visitor ->> Biometric Device: Scan QR Code
    Biometric Device ->> Gate/Door: Open (if valid)
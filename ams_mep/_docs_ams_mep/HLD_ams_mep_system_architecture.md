# High-Level Design (HLD) Document
## AMS MEP - Ministry of Economy and Planning Visitor Management System

**Document Version:** 1.0  
**Date:** 2025-01-08  
**Author:** Software Architecture Team  
**Target Audience:** Stakeholders, Developers, System Integrators  

---

## 1. Executive Summary

The AMS MEP (Access Management System - Ministry of Economy and Planning) is a comprehensive visitor management solution built on Odoo 18 framework. It provides secure visitor registration, invitation management, access control integration, and real-time monitoring capabilities specifically customized for government building requirements.

### 1.1 Key Features
- **Visitor Management**: Complete visitor lifecycle from registration to exit
- **QR Code-based Access Control**: Secure, contactless entry system
- **BioStar 2 Integration**: Hardware access control system integration
- **SAML Authentication**: Enterprise-grade authentication via auth_saml
- **Email Notifications**: Automated invitation and notification system
- **Security Controls**: Debug access restrictions and security monitoring
- **Multi-language Support**: Arabic and English interface

---

## 2. System Architecture Overview

### 2.1 Module Hierarchy and Dependencies

```mermaid
graph BT;
    hr[HR Module<br/>Odoo Core]
    base[Base Module<br/>Odoo Core]
    mail[Mail Module<br/>Odoo Core]
    
    ams_base[ams_base<br/>Foundation Module]
    ams[ams<br/>Core Access Management]
    ams_bs[ams_bs<br/>BioStar Integration]
    ams_vm[ams_vm<br/>Visitor Management]
    ams_mep[ams_mep<br/>MEP Customizations]
    
    auth_saml[auth_saml<br/>SAML Authentication<br/>OCA Module]
    
    ams_base --> hr
    ams_base --> mail
    ams_base --> base
    ams --> ams_base
    ams_bs --> ams
    ams_vm --> ams
    ams_mep --> ams_vm
    ams_mep --> ams_bs
    ams_mep -.-> auth_saml
    
    style ams_mep fill:#ff9999,stroke:#333,stroke-width:3px
    style auth_saml fill:#99ccff,stroke:#333,stroke-width:2px
```

### 2.2 Core Components

| Component | Purpose | Key Responsibilities |
|-----------|---------|---------------------|
| **ams_base** | Foundation Layer | Abstract models, common utilities, base API framework |
| **ams** | Core AMS | Access control entities, user management, device abstraction |
| **ams_bs** | BioStar Integration | Hardware API clients, device synchronization, event logging |
| **ams_vm** | Visitor Management | Visitor registration, invitation workflow, visit tracking |
| **ams_mep** | MEP Customizations | Government-specific UI/UX, security enhancements, reporting |

---

## 3. Technology Stack

### 3.1 Core Technologies
- **Framework**: Odoo 18 Community Edition
- **Language**: Python 3.11+
- **Database**: PostgreSQL 15+
- **Web Server**: Nginx with Proxy Manager
- **Containerization**: Docker & Docker Compose

### 3.2 Integration Technologies
- **Access Control**: BioStar 2 API (REST/JSON)
- **Authentication**: SAML 2.0 (pysaml2 library)
- **Email**: SMTP Integration
- **QR Codes**: Python QR Code generation
- **Reporting**: Odoo Report Engine (QWeb)

### 3.3 Security Technologies
- **Authentication**: Multi-factor (SAML + Odoo Portal)
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: TLS/SSL, Database encryption
- **Session Management**: Secure session handling
- **Audit Logging**: Comprehensive security event logging

---

## 4. System Architecture Diagrams

### 4.1 Deployment Architecture

```mermaid
graph TD
    subgraph "Production Environment"
        subgraph "VM1 - Web Application"
            direction TB
            nginx[Nginx Proxy Manager<br/>Ports: 80, 443, 81]
            odoo[Odoo 18 Application<br/>Ports: 8069, 7072]
            pgadmin[pgAdmin4<br/>Port: 5050]
            portainer[Portainer<br/>Ports: 9000, 9443]
        end
        
        subgraph "VM2 - Database"
            postgres[PostgreSQL 15<br/>Port: 5432]
        end
        
        odoo --> postgres
        pgadmin --> postgres
    end
    
    subgraph "External Systems"
        biostar[BioStar 2 Server<br/>Access Control API]
        ldap[LDAP/Active Directory<br/>Port: 389]
        smtp[SMTP Server<br/>Email Notifications]
        devices[Access Control Devices<br/>Gates, Doors, Scanners]
    end
    
    subgraph "External Services"
        github[GitHub Repository]
        dockerhub[Docker Hub Registry]
    end
    
    odoo <--> biostar
    odoo <--> ldap
    odoo --> smtp
    biostar <--> devices
    odoo <-- github
    odoo <-- dockerhub
    
    style "VM1 - Web Application" fill:#e1f5fe
    style "VM2 - Database" fill:#f3e5f5
    style "External Systems" fill:#fff3e0
```

### 4.2 Application Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        web[Web Interface<br/>Odoo Backend]
        mobile[Mobile Interface<br/>QR Scanner Apps]
        reports[Reports & Analytics<br/>QWeb Templates]
    end
    
    subgraph "Business Logic Layer"
        subgraph "AMS MEP Module"
            security[Security Controllers<br/>Debug Restrictions]
            invitation[Invitation Management<br/>Custom Views & Logic]
            visitor_mep[Visitor Extensions<br/>MEP-specific Fields]
        end
        
        subgraph "AMS VM Module"
            visitor_mgmt[Visitor Management<br/>Registration & Tracking]
            invitation_core[Invitation Core<br/>Workflow & Approval]
            visit_mgmt[Visit Management<br/>Entry/Exit Tracking]
        end
        
        subgraph "AMS BS Module"
            api_clients[BioStar API Clients<br/>AC, User, Device APIs]
            sync_engine[Synchronization Engine<br/>Bidirectional Sync]
            event_processor[Event Processing<br/>Real-time Monitoring]
        end
        
        subgraph "AMS Core Module"
            access_control[Access Control<br/>Groups, Levels, Schedules]
            user_mgmt[User Management<br/>AMS Users & Cards]
            device_mgmt[Device Management<br/>Doors, Devices, Groups]
        end
    end
    
    subgraph "Data Layer"
        models[Odoo ORM Models]
        postgres_db[(PostgreSQL Database)]
        sequences[Sequence Generators]
    end
    
    subgraph "Integration Layer"
        biostar_api[BioStar 2 REST API]
        saml_provider[SAML Identity Provider]
        email_service[SMTP Email Service]
    end
    
    web --> security
    web --> invitation
    mobile --> visitor_mgmt
    reports --> models
    
    security --> visitor_mep
    invitation --> invitation_core
    visitor_mep --> visitor_mgmt
    
    visitor_mgmt --> models
    invitation_core --> models
    visit_mgmt --> models
    
    api_clients --> biostar_api
    sync_engine --> models
    event_processor --> models
    
    access_control --> models
    user_mgmt --> models
    device_mgmt --> models
    
    models --> postgres_db
    sequences --> postgres_db
    
    security -.-> saml_provider
    invitation_core --> email_service
    
    style "AMS MEP Module" fill:#ffcdd2
    style "AMS VM Module" fill:#c8e6c9
    style "AMS BS Module" fill:#bbdefb
    style "AMS Core Module" fill:#fff9c4
```

---

## 5. Data Flow Architecture

### 5.1 Visitor Registration and Access Flow

```mermaid
sequenceDiagram
    participant E as Employee
    participant W as Web Interface
    participant VM as AMS VM Module
    participant MEP as AMS MEP Module
    participant BS as AMS BS Module
    participant Bio as BioStar 2
    participant Email as SMTP Server
    participant V as Visitor
    participant Device as Access Device

    E->>W: Create Invitation Request
    W->>MEP: Process Custom Fields
    MEP->>VM: Create Invitation Record
    VM->>VM: Generate QR Code
    VM->>Email: Send Invitation Email
    Email->>V: Deliver QR Code
    
    V->>Device: Present QR Code
    Device->>Bio: Validate Access
    Bio->>BS: Query Access Rights
    BS->>VM: Verify Invitation Status
    VM->>BS: Confirm Access
    BS->>Bio: Grant/Deny Access
    Bio->>Device: Open/Close Gate
    Device->>BS: Log Access Event
    BS->>VM: Create Visit Record
```

---

## 6. Integration Points

### 6.1 BioStar 2 Integration
- **API Type**: REST/JSON
- **Authentication**: Session-based tokens
- **Endpoints**: Users, Devices, Access Control, Events
- **Sync Frequency**: Real-time for events, scheduled for master data
- **Data Flow**: Bidirectional synchronization

### 6.2 SAML Authentication Integration
- **Provider**: OCA auth_saml module
- **Protocol**: SAML 2.0
- **Identity Provider**: Government LDAP/AD
- **User Mapping**: Email-based user matching
- **Session Management**: Integrated with Odoo session handling

### 6.3 Email Integration
- **Protocol**: SMTP
- **Templates**: QWeb-based email templates
- **Attachments**: QR codes, invitation badges
- **Localization**: Arabic and English support

---

## 7. Security Architecture

### 7.1 Authentication Layers
1. **SAML Authentication**: Primary authentication via government IdP
2. **Odoo Portal Authentication**: Secondary authentication for portal users
3. **API Token Authentication**: For mobile and external integrations

### 7.2 Security Controls (MEP Module)
- **Debug Access Restriction**: Admin-only debug mode access
- **Rate Limiting**: Failed login attempt protection
- **Session Management**: Configurable timeout settings
- **Audit Logging**: Comprehensive security event tracking
- **Database Manager Restrictions**: Limited access to DB management

### 7.3 Data Protection
- **Encryption**: TLS/SSL for data in transit
- **Database Security**: PostgreSQL role-based access
- **Personal Data**: GDPR-compliant visitor data handling
- **Audit Trail**: Complete visitor activity logging

---

## 8. Performance and Scalability

### 8.1 System Specifications
- **Production VM1**: 8 cores, 16GB RAM, 250GB storage
- **Production VM2**: 8 cores, 16GB RAM, 500GB storage
- **Network**: Static IPs, dedicated VLAN
- **Load Balancing**: Nginx reverse proxy

### 8.2 Scalability Considerations
- **Horizontal Scaling**: Multi-instance Odoo deployment capability
- **Database Optimization**: Indexed fields, query optimization
- **Caching**: Redis integration for session and data caching
- **CDN**: Static asset delivery optimization

---

## 9. Monitoring and Maintenance

### 9.1 System Monitoring
- **Application Monitoring**: Odoo built-in logging
- **Database Monitoring**: PostgreSQL performance metrics
- **Infrastructure Monitoring**: Docker container health checks
- **Security Monitoring**: Failed login attempts, suspicious activities

### 9.2 Backup and Recovery
- **Database Backups**: Automated daily PostgreSQL dumps
- **Application Backups**: Docker volume snapshots
- **Configuration Backups**: Git-based configuration management
- **Recovery Procedures**: Documented disaster recovery plans

---

## 10. Development and Deployment

### 10.1 Development Workflow
- **Version Control**: Git with feature branch workflow
- **Code Quality**: Python linting, Odoo coding standards
- **Testing**: Unit tests, integration tests
- **Documentation**: Inline code documentation, API docs

### 10.2 Deployment Pipeline
- **Containerization**: Docker-based deployment
- **Environment Management**: Development, staging, production
- **Configuration Management**: Environment-specific configs
- **Rollback Procedures**: Quick rollback capabilities

---

## 11. Future Enhancements

### 11.1 Planned Features
- **Mobile Application**: Native mobile app for visitors
- **Advanced Analytics**: Business intelligence dashboards
- **AI Integration**: Facial recognition, behavior analysis
- **IoT Integration**: Environmental sensors, occupancy tracking

### 11.2 Scalability Roadmap
- **Multi-tenant Architecture**: Support for multiple government entities
- **Cloud Migration**: AWS/Azure deployment options
- **API Gateway**: Centralized API management
- **Microservices**: Gradual migration to microservices architecture

---

## 12. Conclusion

The AMS MEP system provides a robust, secure, and scalable visitor management solution tailored for government requirements. The modular architecture ensures maintainability and extensibility while the comprehensive security framework addresses government-grade security needs.

The system successfully integrates multiple technologies and external systems to provide a seamless visitor experience while maintaining strict security controls and audit capabilities.

---

**Document Control:**
- **Created**: 2025-01-08
- **Last Modified**: 2025-01-08
- **Review Cycle**: Quarterly
- **Approval**: Pending stakeholder review

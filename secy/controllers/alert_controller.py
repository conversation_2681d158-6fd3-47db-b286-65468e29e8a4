from odoo import http, fields
from odoo.addons.secy.controllers.base_controller import BaseController
from odoo.http import request



class Alert<PERSON>ontroller(BaseController):
    @http.route('/GetNewAlerts', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_new_alerts(self, **kwargs):
        try:
            sys_id = request.params.get('SysID', 1)
            result = self.alert_model.get_new_alerts(sys_id)
            self.logger.info(f"new alerts count:{len(result)}")
            return request.make_json_response(result, status=200)

        except Exception as ex:
            self.logger.error('An error occurred while getting new alerts: %s', ex)
            return request.make_json_response([], status=200)

    @http.route('/GetAlerts', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_alerts(self, **kwargs):
        try:
            sys_id = request.params.get('SysID', 1)
            result = self.alert_model.get_alerts(sys_id)
            self.logger.info(f"all alerts count:{len(result)}")
            return request.make_json_response(result, status=200)

        except Exception as ex:
            self.logger.error('An error occurred while getting alerts: %s', ex)
            return request.make_json_response([], status=200)

    @http.route('/RemoveAlert',  type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def remove_alert(self, id,  SysID = 1):
            """
            Mark the alert as 'deleted' by updating its state.
            Expects 'id' and optional 'SysID'.
            """
            try:
                # Call the remove_alert function in the model
                result = self.alert_model.remove_alert(id,int(SysID))
                self.logger.info(f'remove_alert result : {result}')
                return request.make_json_response(result, status=200)
            except Exception as ex:
                # Log the error and return an error response
                self.logger.error('An error occurred while removing the alert: %s', ex)
                return request.make_json_response(-2, status=200)

    @http.route('/GetMemos', type='http', auth='public', methods=['GET'], csrf=False, cors="*")
    def get_memos(self, **kwargs):

        try:
            sys_id = request.params.get('SysID', 0)
            result = self.memo_model.get_memo(sys_id)
            self.logger.info(f"memos count:{len(result)}")
            return request.make_json_response(result, status=200)

        except Exception as ex:
            self.logger.error('An error occurred while getting memos: %s', ex)
            return request.make_json_response([], status=200)

    @http.route('/SetMemoStatus', type='http', auth='public', methods=['GET'], csrf=False)
    def set_memo_status(self, id, status, SysID=1):
        # Set the visit status in the database
        result = self.memo_model.set_entry_state(id, int(status), int(SysID))
        self.logger.info(f'set_memo_status result : {result}')
        return request.make_json_response(result, status=200)

    @http.route('/PostMemo', type='http', auth='public', methods=['POST'], csrf=False)
    def post_memo(self, **kwargs):
        # Convert incoming JSON data to MemoDTO
        request_data = request.get_json_data()
        memo_data = request_data.get('memo',{})
        result = self.memo_model.post_memo(memo_data)
        self.logger.info(f"PostMemo result:{result}")
        return request.make_json_response(result, status=200)

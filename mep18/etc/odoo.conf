; /etc/odoo/odoo.conf ; /mnt/odoo17e/odoo/addons
[options]
addons_path = /usr/lib/python3/dist-packages/odoo/addons,/mnt/odoo18/addons_thp/muk_web_theme,/mnt/odoo18/addons_thp/oca,/mnt/odoo18/addons_thp/lp,/mnt/extra-addons
logfile = /var/log/odoo/odoo.log
db_user = odoo
db_password = admin
db_name = MEP_18_202501
;dbfilter = False
db_filter = MEP_18_202501

; Maximum execution time for long polling requests (in seconds)
proxy_mode = True
longpolling_port = 7072
gevent_port = 7072

longpolling_timeout = 300
xmlrpc_port = 8069
xmlrpc_interface = 0.0.0.0
netrpc_interface = 0.0.0.0



;admin_pass=admin
admin_passwd =Admin@123456
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
max_cron_threads = 2
workers = 5
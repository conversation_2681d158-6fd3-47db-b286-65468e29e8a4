====================
SAML2 Authentication
====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:9713ed18925a56b7a75da4faf0e627f8b34a316a93ec46c53ac72d14bcc03d53
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fserver--auth-lightgray.png?logo=github
    :target: https://github.com/OCA/server-auth/tree/18.0/auth_saml
    :alt: OCA/server-auth
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/server-auth-18-0/server-auth-18-0-auth_saml
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/server-auth&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Let users log into Odoo via an SAML2 identity provider.

This module allows to deport the management of users and passwords in an
external authentication system to provide SSO functionality (Single Sign
On) between Odoo and other applications of your ecosystem.

**Benefits**:

- Reducing the time spent typing different passwords for different
  accounts.
- Reducing the time spent in IT support for password oversights.
- Centralizing authentication systems.
- Securing all input levels / exit / access to multiple systems without
  prompting users.
- The centralization of access control information for compliance
  testing to different standards.

**Table of contents**

.. contents::
   :local:

Installation
============

This addon requires the python module ``pysaml2``.

``pysaml2`` requires the binary ``xmlsec1`` (on Debian or Ubuntu you can
install it with ``apt-get install xmlsec1``)

Configuration
=============

To use this module, you need an IDP server, properly set up.

1. Configure the module according to your IdP’s instructions (Settings >
   Users & Companies > SAML Providers).
2. Pre-create your users and set the SAML information against the user.

By default, the module let users have both a password and SAML ids. To
increase security, disable passwords by using the option in Settings.
Note that the admin account can still have a password, even if the
option is activated. Setting the option immediately remove all password
from users with a configured SAML ids.

If all the users have a SAML id in a single provider, you can set
automatic redirection in the provider settings. The autoredirection will
only be done on the active provider with the highest priority. It is
still possible to access the login without redirection by using the
query parameter ``disable_autoredirect``, as in
``https://example.com/web/login?disable_autoredirect=`` The login is
also displayed if there is an error with SAML login, in order to display
any error message.

Usage
=====

Users can login with the configured SAML IdP with buttons added in the
login screen.

Known issues / Roadmap
======================

- clean up ``auth_saml.request``

Changelog
=========

18.0.1.0.2 (2025-05-13)
-----------------------

Bugfixes
~~~~~~~~

- Avoid redirecting when there is a SAML error.

16.0.1.0.0
----------

Initial migration for 16.0.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/server-auth/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/server-auth/issues/new?body=module:%20auth_saml%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* XCG Consulting

Contributors
------------

- `XCG Consulting <https://xcg-consulting.fr/>`__:

  - Florent Aide <<EMAIL>>
  - Vincent Hatakeyama <<EMAIL>>
  - Alexandre Brun
  - Houzéfa Abbasbhay <<EMAIL>>
  - Szeka Wong <<EMAIL>>

- Jeremy Co Kim Len <<EMAIL>>
- Jeffery Chen Fan <<EMAIL>>
- Bhavesh Odedra <<EMAIL>>
- `Tecnativa <https://www.tecnativa.com/>`__:

  - Jairo Llopis

- `GlodoUK <https://www.glodo.uk/>`__:

  - Karl Southern

- `TAKOBI <https://takobi.online/>`__:

  - Lorenzo Battistini

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-vincent-hatakeyama| image:: https://github.com/vincent-hatakeyama.png?size=40px
    :target: https://github.com/vincent-hatakeyama
    :alt: vincent-hatakeyama

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-vincent-hatakeyama| 

This module is part of the `OCA/server-auth <https://github.com/OCA/server-auth/tree/18.0/auth_saml>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.

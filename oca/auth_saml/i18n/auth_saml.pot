# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_saml
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.providers
msgid "- or -"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/controllers/main.py:0
msgid "Access Denied"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__active
msgid "Active"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__css_class
msgid "Add a CSS class that serves you to style the login button."
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Algorithm used to sign requests."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_res_config_settings__allow_saml_uid_and_internal_password
#: model_terms:ir.ui.view,arch_db:auth_saml.auth_saml_base_settings_form
msgid ""
"Allow SAML users to possess an Odoo password (warning: decreases security)"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.auth_saml_provider_view_search
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Archived"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__attribute_mapping_ids
msgid "Attribute Mapping"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__matching_attribute
msgid ""
"Attribute to look for in the returned IDP response to match against an Odoo "
"user."
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"Attribute to match the user in Odoo with against the IDP (Identity "
"Provider). You may use the special case \"subject.nameId\" to match against "
"the nameId in the IDP response."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__authn_requests_signed
msgid "Authn Requests Signed"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__autoredirect
msgid "Automatic Redirection"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"Available after first save. The URL will change if the provider is deleted "
"&amp; recreated or the database is renamed."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__sp_baseurl
msgid ""
"Base URL sent to Odoo with this, rather than automatically\n"
"        detecting from request or system parameter web.base.url"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__css_class
msgid "Button Icon CSS class"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__idp_metadata
msgid ""
"Configuration for this Identity Provider. Supplied by the provider, in XML "
"format."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__create_uid
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__create_uid
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__create_uid
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__create_uid
msgid "Created by"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__create_date
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__create_date
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__create_date
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__create_date
msgid "Created on"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__saml_request_id
msgid "Current Request ID"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__saml_access_token
msgid "Current SAML token for this user"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__display_name
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__display_name
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__display_name
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__display_name
msgid "Display Name"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Display Settings"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__entity_id
msgid "Entity ID"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"Entity Identifier sent to the IDP. Often this would be the metadata URL, but"
" it can be any string."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__entity_id
msgid "EntityID passed to IDP, used to identify the Odoo"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__matching_attribute_to_lower
msgid "Force matching_attribute to lower case before passing back to Odoo."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__id
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__id
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__id
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__id
msgid "ID"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__attribute_name
msgid "IDP Response Attribute"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__idp_metadata
msgid "Identity Provider Metadata"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Identity Provider Settings"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__matching_attribute
msgid "Identity Provider matching attribute"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__authn_requests_signed
msgid ""
"Indicates if the Authentication Requests sent by this SP should be signed by"
" default."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__want_assertions_signed
msgid "Indicates if this SP wants the IdP to send the assertions signed."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__logout_requests_signed
msgid ""
"Indicates if this entity will sign the Logout Requests originated from it."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__want_response_signed
msgid "Indicates that Authentication Responses to this SP must be signed."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__want_assertions_or_response_signed
msgid ""
"Indicates that either the Authentication Response or the assertions "
"contained within the response to this SP must be signed."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__write_uid
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__write_uid
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__write_uid
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__write_uid
msgid "Last Updated by"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__write_date
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__write_date
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__write_date
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__write_date
msgid "Last Updated on"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__body
msgid "Link text in Login Dialog"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__body
msgid "Login button label"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__logout_requests_signed
msgid "Logout Requests Signed"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__matching_attribute_to_lower
msgid "Lowercase IDP Matching Attribute"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"Mapped attributes are copied from the SAML response at every logon, if "
"available. If multiple values are returned (i.e. a list) then the first "
"value is used."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_metadata_url
msgid "Metadata URL"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/controllers/main.py:0
msgid "Missing parameters"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__field_name
msgid "Odoo Field"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_pem_private
msgid "Odoo Private Key"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_pem_private_filename
msgid "Odoo Private Key File Name"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_pem_public
msgid "Odoo Public Certificate"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_pem_public_filename
msgid "Odoo Public Certificate File Name"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Odoo Settings"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__autoredirect
msgid ""
"Only the provider with the higher priority will be automatically redirected"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sp_baseurl
msgid "Override Base URL"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_attribute_mapping__provider_id
msgid "Provider"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__name
msgid "Provider Name"
msgstr ""

#. module: auth_saml
#: model:ir.actions.act_window,name:auth_saml.action_saml_provider
#: model_terms:ir.ui.view,arch_db:auth_saml.auth_saml_provider_view_search
msgid "Providers"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.auth_saml_base_settings_form
#: model_terms:ir.ui.view,arch_db:auth_saml.view_users_form
msgid "SAML"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_auth_saml_request
msgid "SAML Outstanding Requests"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__saml_provider_id
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "SAML Provider"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_request__saml_provider_id
msgid "SAML Provider that issued the token"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_res_users_saml__saml_uid
msgid "SAML Provider user_id"
msgstr ""

#. module: auth_saml
#: model:ir.ui.menu,name:auth_saml.menu_saml_providers
msgid "SAML Providers"
msgstr ""

#. module: auth_saml
#: model:ir.model.constraint,message:auth_saml.constraint_res_users_saml_uniq_users_saml_provider_saml_uid
msgid "SAML UID must be unique per provider"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__saml_uid
msgid "SAML User ID"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_auth_saml_provider
msgid "SAML2 Provider"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_auth_saml_attribute_mapping
msgid "SAML2 attribute mapping"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_res_users__saml_ids
msgid "Saml"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sequence
msgid "Sequence"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sign_authenticate_requests
msgid "Sign Authenticate Requests"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sign_metadata
msgid "Sign Metadata"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/controllers/main.py:0
msgid "Sign up is not allowed on this database."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__sig_alg
msgid "Signature Algorithm"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_ir_config_parameter
msgid "System Parameter"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"The URL configured for the ACS must exactly match what is sent. If you have "
"odoo responding on multiple URLs you can use this to force it to send a "
"specific address rather than rely on automatically detecting."
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_res_users_saml__saml_access_token
msgid "The current SAML token in use"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/models/res_users.py:0
msgid ""
"This database disallows users to have both passwords and SAML IDs. Error for"
" logins %s"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/controllers/main.py:0
msgid "Unknown provider"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid ""
"Used to sign requests sent to the IDP. You can use openssl to generate a "
"certificate and key."
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_res_users
#: model:ir.model.fields,field_description:auth_saml.field_res_users_saml__user_id
msgid "User"
msgstr ""

#. module: auth_saml
#: model:ir.model,name:auth_saml.model_res_users_saml
msgid "User to SAML Provider Mapping"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__want_assertions_or_response_signed
msgid "Want Assertions Or Response Signed"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__want_assertions_signed
msgid "Want Assertions Signed"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,field_description:auth_saml.field_auth_saml_provider__want_response_signed
msgid "Want Response Signed"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__sign_metadata
msgid "Whether metadata should be signed or not"
msgstr ""

#. module: auth_saml
#: model:ir.model.fields,help:auth_saml.field_auth_saml_provider__sign_authenticate_requests
msgid "Whether the request should be signed or not"
msgstr ""

#. module: auth_saml
#. odoo-python
#: code:addons/auth_saml/controllers/main.py:0
msgid "You do not have access to this database. Please contact support."
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Your ACS url will be base_url + /auth_saml/signin"
msgstr ""

#. module: auth_saml
#: model_terms:ir.ui.view,arch_db:auth_saml.view_saml_provider_form
msgid "Your provider will give you this XML once configured."
msgstr ""

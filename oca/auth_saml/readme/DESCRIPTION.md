Let users log into Odoo via an SAML2 identity provider.

This module allows to deport the management of users and passwords in an
external authentication system to provide SSO functionality (Single Sign
On) between Odoo and other applications of your ecosystem.

**Benefits**:

- Reducing the time spent typing different passwords for different
  accounts.
- Reducing the time spent in IT support for password oversights.
- Centralizing authentication systems.
- Securing all input levels / exit / access to multiple systems without
  prompting users.
- The centralization of access control information for compliance
  testing to different standards.

<odoo>
    <record id="paperformat_ucaf1_report_formt" model="report.paperformat">
            <field name="name">US Letter</field>
            <field name="default" eval="True" />
            <field name="format">A4</field>
            <field name="page_height">0</field>
            <field name="page_width">0</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">5</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">7</field>
            <field name="margin_right">7</field>
            <field name="header_line" eval="False" />
            <field name="header_spacing">0</field>
            <field name="dpi">140</field>
        </record>

    <record id="op_ucaf_report" model="ir.actions.report">
        <field name="name">UCAF Report</field>
        <field name="model">emr_op.visit_summary_report</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">emr_op.op_ucaf_template</field>
        <field name="report_file">emr_op.op_ucaf_template</field>
        <field name="attachment_use" eval="True"/>
        <field name="paperformat_id" ref="paperformat_ucaf1_report_formt"/>
        <field name="binding_model_id" ref="model_emr_op_visit_summary_report"/>
        <field name="binding_type">report</field>
    </record>

    <record id="patient_medical_report" model="ir.actions.report">
        <field name="name">Patient Medical Report</field>
        <field name="model">emr_op.visit_summary_report</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">emr_op.op_patient_medical_template</field>
        <field name="report_file">emr_op.op_patient_medical_template</field>
        <field name="attachment_use" eval="True"/>
        <field name="paperformat_id" ref="paperformat_ucaf1_report_formt"/>
        <field name="binding_model_id" ref="model_emr_op_visit_summary_report"/>
        <field name="binding_type">report</field>
    </record>
</odoo>
# -*- coding: utf-8 -*-
from datetime import timedelta, datetime

from odoo import api, fields, models, _

from odoo.exceptions import UserError, ValidationError
import uuid


class Claim(models.Model):
    # region ---------------------- TODO[IMP]: Private Attributes --------------------------------
    _name = "emr_coverage.claim"
    _inherit = [ "emr_coverage.claim", "emr_nphies.communication_manager", "emr_nphies.abstract_request"]

    # endregion

    # region ---------------------- TODO[IMP]:Default Methods ------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Fields Declaration ---------------------------------
    # region  Basic
    eligibility_offline_date = fields.Datetime(compute="_compute_offline_dates", store=True)
    authorization_offline_date = fields.Datetime()
    claim_ref = fields.Char()  # Claim.related.claim:Ref

    approved_tax = fields.Float(default=0.0)
    approved_payer_share_price = fields.Float(default=0.0)
    approved_qty = fields.Integer(default=1, string="Approved Quantity")
    approved_patient_share = fields.Float(default=0.0)
    approved_total_price = fields.Float(compute='_compute_approved_total_price', store=True)

    claim_ref_relationship_id = fields.Many2one("emr_term.code_concept", help="vs_oid= related-claim-relationship",
                                                domain=lambda self: self._domain_code_concept(
                                                    vs_oid='related-claim-relationship'))  # Claim.related.relationship, vs_oid= related-claim-relationship
    pre_auth_ref = fields.Char(compute="_compute_pre_auth_ref", store=True,
                               help="Claim.insurance.preAuthRef")  # Reference numbers previously provided by the insurer to the provider to be quoted on subsequent claims containing services or products related to the prior authorization.

    # ------- accident - ------------------------- copy
    accident = fields.Boolean(default=False)
    accident_date = fields.Date()
    accident_type_id = fields.Many2one('emr_term.code_concept',
                                       domain=lambda self: self._domain_code_concept(cs_oid='v3-ActIncidentCode'))
    accident_address_city = fields.Char(string="City")
    accident_address_text = fields.Char(string="Text")
    accident_address_district = fields.Char(string="District")
    accident_address_state = fields.Char(string="State")
    accident_address_postal_code = fields.Char(string="Postal Code")
    accident_address_country_id = fields.Many2one('res.country', string="Country")
    accident_address_start_date = fields.Datetime(string="Start Date")
    accident_address_end_date = fields.Datetime(string="End Date")
    newborn = fields.Boolean()
    include_encounter = fields.Boolean()
    include_references = fields.Boolean(default=True,
                                        help="Include Eligibility/Authorisation references offline/online")
    # -------------------------------------------------------------------------------
    # endregion

    # region  Special
    # endregion

    # region  Relational
    # task_ids = fields.One2many('emr_nphies.task', 'claim_request_id')
    batch_manager_id = fields.Many2one(related='claim_batch_id.request_manager_id', store=True)

    reissue_reason_id = fields.Many2one('emr_term.code_concept', help="cs_oid=reissue-reason", string="Re-issue reason",
                                        domain=lambda reason: reason._domain_code_concept(cs_oid='reissue-reason'))
    reissue_reason_code = fields.Char(related='reissue_reason_id.code', string="Re-issue reason code")

    # endregion

    # region  Computed
    # endregion

    # endregion

    # region ---------------------- TODO[IMP]: Compute methods ------------------------------------
    @api.depends('claim_line_ids', 'approved_tax', 'approved_patient_share', 'approved_payer_share_price',
                 'approved_qty')
    def _compute_approved_total_price(self):
        """
        Compute the approved total price based on the sum of approved total prices from related claim lines.
        """
        for rec in self:
            rec.approved_total_price = 0.0
            rec.approved_total_price = sum(rec.claim_line_ids.mapped('approved_total_price'))

    @api.depends("claim_line_ids")
    def _compute_offline_dates(self):
        """
        Compute offline dates based on claim line information.
        """
        for rec in self:
            if rec.claim_line_ids:
                product_lines = rec.claim_line_ids.mapped('product_line_id')
                approval_requests = [line.approval_line_id.request_id for line in product_lines
                                     if line.approval_line_id and not line.approval_line_id.request_id.response_outcome_id]
                if approval_requests:
                    rec.authorization_offline_date = approval_requests[0].checkout_datetime
                else:
                    rec.authorization_offline_date = False

                if rec.visit_id.default_invoice_id.last_eligibility_request_id:
                    rec.eligibility_offline_date = False
                else:
                    rec.eligibility_offline_date = rec.visit_id.default_invoice_id.date
            else:
                rec.eligibility_offline_date = False

    @api.depends("approval_request_id")
    def _compute_pre_auth_ref(self):
        """
        Compute the pre-authorization reference for each record based on the approval request ID.
        """
        for rec in self:
            rec.pre_auth_ref = ''
            if rec.approval_request_id:
                rec.pre_auth_ref = rec.approval_request_id.preauth_ref_no
                if rec.approval_request_id.related_approval_request_id:
                    if rec.approval_request_id.related_approval_request_id.preauth_ref_no != rec.pre_auth_ref:
                        if not rec.pre_auth_ref:
                            rec.pre_auth_ref = rec.approval_request_id.related_approval_request_id.preauth_ref_no or ""
                        else:
                            rec.pre_auth_ref += f', {rec.approval_request_id.related_approval_request_id.preauth_ref_no or ""}'

    # @api.depends("patient_class_id")
    # def _compute_claim_ref(self):
    #     for rec in self:
            # rec.claim_ref = ''
            # if rec.visit_patient_id:
            #     rec.claim_ref_relationship_id = rec.patient_class_id.relationship_id
            #     rec.claim_ref = rec.patient_class_id.subscriber_id.code if rec.patient_class_id.subscriber_id else ''
    # endregion

    # region ---------------------- TODO[IMP]: Constrains and Onchanges ---------------------------
    # endregion

    # region ---------------------- TODO[IMP]: CRUD Methods -------------------------------------
    # endregion

    # region ---------------------- TODO[IMP]: Action Methods -------------------------------------
    def action_send_request(self):
        """
        A function to send a request if conditions are met.
        """
        if self.request_manager_status == 'cancelled':
            self._regenerate_request_manager()
        if (self.create_date_custom <= datetime.now()
                or self.env['ir.config_parameter'].sudo().get_param('emr_nphies.claim_items_date_before', 0)):
            self._send_request(self.message_event_code)
        else:
            self.warning_msg = _("Request can't be sent before %s", self.create_date_custom)

    def _request_checkin(self):
        """
        A private method that performs a check-in request.
        """
        self.action_checkin()

    def _request_checkout(self):
        """
        Request checkout and perform some action with sudo privileges.
        """
        self.sudo().action_checkout()

    # def _regenerate_request_manager(self):
    #     """to override in each request to create new request manager id
    #     to resend request again to nphies portal if there is duplicate message"""
    #
    #     new_manger_id = self.create_request_manger(request=self)
    #     return new_manger_id
    def action_fix_empty_fields(self):
        """
        Fix empty fields by computing values for `visit_type` based on certain conditions.
        """
        for rec in self:
            # rec._compute_claim_ref()
            rec._compute_pre_auth_ref()
            rec.visit_type = 'follow-up' \
                if rec.visit_id.default_service_id and rec.visit_id.parent_visit_id else 'consultation'

    # endregion

    # region ---------------------- TODO[IMP]: Business Methods -------------------------------------
    # region property implementation
    @property
    def patient(self):
        """override method  on the request to return the patient"""
        return self.visit_patient_id

    @property
    def company(self):
        """override method  on the request to return the company (HCP)"""
        return self.company_id

    @property
    def cov_company(self):
        """override method  on the request to return the insurance | charity organization (HCI)"""
        return self.visit_cov_company_id

    @property
    def visit(self):
        return self.visit_id

    @property
    def claim_type(self):
        return self.type_id.code

    @property
    def req_type(self):
        return "Claim"

    @property
    def location(self):
        """override method  on the request to return the insurance | charity organization (HCI)"""
        return self.visit_id.unit_id

    @property
    def message_event_code(self):
        """must override method in all requests to set request message code"""
        return "claim-request"

    @property
    def response_message_event_code(self):
        return 'claim-response'

    @property
    def request_resource_id(self):
        """TODO add it in nphies abstarct and delete from here"""
        return self.request_manager_id.id

    @property
    def request_profile(self):
        """to be override , default already exist in the base abstract, terminology abstract """
        return super(Claim, self).profile

    @property
    def bundle_reference(self):
        return f'{self.company.base_url}/Bundle/{self.bundle_id}'

    # ------- Properties inherited from base_claim_request-------

    @property
    def _get_use(self):
        return "claim"

    @property
    def _get_created_date(self):
        create_date = self.create_date_custom
        days_offset = self.env['ir.config_parameter'].sudo().get_param('emr_nphies.claim_items_date_before', 0)
        if self.create_date_custom > datetime.now() and days_offset:
            create_date = (datetime.now() - timedelta(days=1))
            self.create_date_custom = create_date
        return self.to_iso_sa_format(create_date)

    @property
    def _get_type(self):
        return self.type_id

    @property
    def _get_sub_type(self):
        return self.sub_type_id

    @property
    def _get_total(self):
        return round(self.price_total_round, 2)

    def _get_items(self, to_dict=True):
        """
        Get items based on certain conditions.

        :param to_dict: bool, whether to return items as dictionaries
        :return: list of items
        """
        if to_dict:
            return [item._to_dict(hcp_url=self.company.base_url) for item in self.claim_line_ids if item.product_line_id]
        return self.claim_line_ids
    # endregion

    def _to_dict(self, **kwargs):
        """
        A function to convert the object to a dictionary format with additional data.
        """
        dict = self.env["emr_nphies.base_claim_request"]._to_dict_common(self, sub_type = self.sub_type_id.code, **kwargs)
        dict['resource'].update({})
        # region ----------------- related ----------------
        if self.claim_ref:
            dict['resource']['related'] = [
                    {
                        "claim": {
                            "type": "Claim",
                            "identifier": {
                                "system": "http://sgh.sa.com/claim",
                                "value": self.claim_ref
                            }
                        },
                        "relationship": {
                            "coding": [
                                {
                                    "system": self.claim_ref_relationship_id.cs_url if self.claim_ref_relationship_id else
                                    "http://nphies.sa/terminology/CodeSystem/related-claim-relationship",

                                    "code": self.claim_ref_relationship_id.code if self.claim_ref_relationship_id else "prior"
                                }
                            ]
                        }
                    }
                ]
        # endregion
        # region ----------------- Episode -----------------
        nphies_url = kwargs.get('nphies_url') or "http://nphies.sa"
        extensions = [
            {
                "url": f"{nphies_url}/fhir/ksa/nphies-fs/StructureDefinition/extension-episode",
                "valueIdentifier": {
                    "system": f"{nphies_url}/extension-episode",
                    "value": f"{self.episode_no}"
                }
            }
        ]
        # endregion
        # region ----------------- Batch --------------------
        if self._context.get('batch_request') and self.claim_batch_id:
            batch_extensions = self.claim_batch_id._prepare_batch_extensions(self)
            extensions += batch_extensions
        # endregion
        # region -------------- PreAuth ---------------------
        preauth_references = []
        if self.approval_request_id:
            if self.approval_request_id.preauth_ref_no:
                preauth_references.append(self.approval_request_id.preauth_ref_no)

            if (self.approval_request_id.related_approval_request_id and
                    self.approval_request_id.related_approval_request_id.preauth_ref_no):
                if self.approval_request_id.related_approval_request_id.preauth_ref_no not in preauth_references:
                    preauth_references.append(self.approval_request_id.related_approval_request_id.preauth_ref_no)

        insurance_list = self.request_manager_id.get_field_value(dict, 'resource.insurance') or []
        if insurance_list and preauth_references and self.include_references:
            for ins_dict in insurance_list:
                ins_dict.update(preAuthRef=preauth_references)
            dict['resource'].update(insurance=insurance_list)

        if self.include_references and self.approval_request_id:
            if self.approval_request_id.response_identifier:
                extensions.append({
                    "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-priorauth-response",
                    "valueReference": {
                        "identifier":
                            {
                                "system": self.approval_request_id.response_system,
                                "value": self.approval_request_id.response_identifier
                            }
                    }
                })
            else:
                authorization_offline_date = self.authorization_offline_date if self.authorization_offline_date else \
                    self.approval_request_id.checkout_datetime
                extensions.append({
                    "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-authorization-offline-date",
                    "valueDateTime": authorization_offline_date.strftime("%Y-%m-%d")
                })
        # endregion
        if dict['resource'].get('extension'):
            dict['resource']['extension'].extend(extensions)
        else:
            dict['resource']['extension'] = extensions

        return dict

    def _to_dict_old(self, **kwargs):
        # 333333333333333profile = self.get_profile('emr_coverage.claim')
        # hcp_url= kwargs.get('hcp_url')
        # self.company.base_url
        # TODO add technical settings
        create_date = self.create_date_custom
        days_offset = self.env['ir.config_parameter'].sudo().get_param('emr_nphies.claim_items_date_before', 0)
        if self.create_date_custom > datetime.now() and days_offset:
            create_date = (datetime.now() - timedelta(days=1))
            self.create_date_custom = create_date

        profile = self.profile

        nphies_url = kwargs.get('nphies_url') or "http://nphies.sa"

        data_dict = {
            "fullUrl": f"{self.company.base_url}/Claim/{self.request_manager_id.id}",
            # {{ReqClaimReqMainResourceId1}}",
            "resource": {
                "resourceType": "Claim",
                "id": self.request_manager_id.id,  # "{{ReqClaimReqMainResourceId1}}",
                "meta": {
                    "profile": [profile.url
                                # "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/institutional-claim|1.0.0"
                                ]
                },
                "extension": [
                    {
                        "url": f"{nphies_url}/fhir/ksa/nphies-fs/StructureDefinition/extension-episode",
                        "valueIdentifier": {
                            "system": f"{nphies_url}/extension-episode",
                            "value": f"{self.episode_no}"
                        }
                    },
                    # {
                    #     "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-eligibility-offline-reference",
                    #     "valueString": "Pseudo-payer-Portal-Elig-0973"
                    # },
                    # {
                    #     "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-eligibility-offline-date",
                    #     "valueDateTime": "2021-06-30T11:01:20+03:00"
                    # }
                ],
                "identifier": [
                    {
                        "system": self.request_manager_id.identifier_system,
                        "value": f"{self.request_identifier}"
                    }
                ],
                "status": "active",
                "type": {
                    "coding": [
                        {
                            "system": self.type_id.cs_url,
                            "code": self.type_id.code
                        }
                    ]
                },
                "use": "claim",
                "patient": {
                    "reference": f"{self.company.base_url}/Patient/{self.patient.id}"
                    # "{self.company.base_url/Patient/30"
                },
                "created": self.to_iso_sa_format(create_date),
                "insurer": {  # base_url
                    "reference": f"{self.company.base_url}/Organization/{self.cov_company._identifier}"
                },
                "provider": {
                    "reference": f"{self.company.base_url}/Organization/{self.company._identifier}"
                },
                "priority": {
                    "coding": [
                        {
                            "system": self.priority_id.cs_url,
                            "code": self.priority_id.code
                        }
                    ]
                },
                "payee": {
                    "type": {
                        "coding": [
                            {
                                "system": "http://terminology.hl7.org/CodeSystem/payeetype",
                                "code": "provider"
                            }
                        ]
                    }
                },

                "careTeam": [member._to_dict(hcp_url=self.company.base_url) for member in
                             self.claim_request_manager_id.care_team_ids],

                "supportingInfo": [support_line._to_dict() for support_line in self.support_info_line_ids],

                "insurance": [
                    {
                        "sequence": self.patient_class_id.id,  # TODO: ??
                        "focal": True,
                        "coverage": {
                            # TODO: add to_dict() in patient class,add missing codeabale concept copy from the patient
                            "reference": f"{self.company.base_url}/Coverage/{self.patient_class_id.id}"
                            # TODO: ?? what is the '1'
                        }
                    }
                ],
                "item": [item._to_dict(hcp_url=self.company.base_url) for item in self.claim_line_ids if
                         item.product_line_id],
                "total": {
                    "value": round(self.price_total_round, 2),
                    "currency": "SAR"
                }
            }
        }
        if self.claim_request_manager_id.diagnosis_line_ids:
            data_dict['resource']['diagnosis'] = [line._to_dict(claim_type=self.type_id.code) for line in
                                                  self.claim_request_manager_id.diagnosis_line_ids]

        if self.claim_ref:
            data_dict['resource']['related'] = [
                {
                    "claim": {
                        "type": "Claim",
                        "identifier": {
                            "system": "http://sgh.sa.com/claim",
                            "value": self.claim_ref
                        }
                    },
                    "relationship": {
                        "coding": [
                            {
                                "system": self.claim_ref_relationship_id.cs_url if self.claim_ref_relationship_id else
                                "http://nphies.sa/terminology/CodeSystem/related-claim-relationship",

                                "code": self.claim_ref_relationship_id.code if self.claim_ref_relationship_id else "prior"
                            }
                        ]
                    }
                }
            ]

        if self.sub_type_id:
            data_dict['resource']['subType'] = {
                "coding": [
                    {
                        "system": self.sub_type_id.cs_url,
                        "code": self.sub_type_id.code
                    }
                ]
            }
        if self._context.get('batch_request') and self.claim_batch_id:
            batch_extensions = self.claim_batch_id._prepare_batch_extensions(self)
            data_dict['resource']['extension'] += batch_extensions

        if self.type_code == 'vision' and self.vision_prescription_lens_line_ids:
            profile = self.vision_prescription_id.profile.display if self.vision_prescription_id.profile else False
            vision_prescription_full_url = f"{self.company.base_url}/{profile}/{self.vision_prescription_id.id}"
            data_dict['resource']['prescription'] = {'reference': vision_prescription_full_url}

        if self.newborn and self.patient.is_newborn:
            data_dict['resource']['extension'] += [{
                "url": f"{self.request_manager_id.nphies_base_url}/fhir/ksa/nphies-fs/StructureDefinition/extension-newborn",
                "valueBoolean": True
            }]
        # Old approval business
        # product_lines = self.claim_line_ids.mapped('product_line_id')
        # approval_requests = product_lines.mapped('approval_line_id.request_id') if product_lines else []
        # preauth_references = approval_requests.mapped('preauth_ref_no') if approval_requests else []
        preauth_references = []
        if self.approval_request_id:
            if self.approval_request_id.preauth_ref_no:
                preauth_references.append(self.approval_request_id.preauth_ref_no)

            if (self.approval_request_id.related_approval_request_id and
                    self.approval_request_id.related_approval_request_id.preauth_ref_no):
                if self.approval_request_id.related_approval_request_id.preauth_ref_no not in preauth_references:
                    preauth_references.append(self.approval_request_id.related_approval_request_id.preauth_ref_no)

        insurance_list = self.request_manager_id.get_field_value(data_dict, 'resource.insurance') or []
        if insurance_list and preauth_references and self.include_references:
            for ins_dict in insurance_list:
                ins_dict.update(preAuthRef=preauth_references)
            data_dict['resource'].update(insurance=insurance_list)
        extensions = self.request_manager_id.get_field_value(data_dict, 'resource.extension') or []
        # if approval_requests and self.include_references:
        # extensions.append({
        #     "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-priorauth-response",
        #     "identifier": [
        #         {
        #             "system": f"{self.company_id.base_url}/authorizationresponse",
        #             "value": apr_req.response_identifier
        #
        #         } for apr_req in approval_requests if apr_req.request_identifier]
        # })
        # extension_priorauth_responses = []
        # for apr_req in approval_requests:
        #     if apr_req.response_identifier and not apr_req.related_approval_request_id:
        #         extension_priorauth_responses.append({
        #             "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-priorauth-response",
        #             "valueReference": {
        #                 "identifier":
        #                     {
        #                         "system": apr_req.response_system,
        #                         "value": apr_req.response_identifier
        #                     }
        #             }
        #         })
        #     else:
        #         authorization_offline_date = self.authorization_offline_date if self.authorization_offline_date else \
        #             apr_req.checkout_datetime
        #         extension_priorauth_responses.append({
        #             "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-authorization-offline-date",
        #             "valueDateTime": authorization_offline_date.strftime("%Y-%m-%d")
        #         })
        if self.include_references and self.approval_request_id:
            if self.approval_request_id.response_identifier:
                extensions.append({
                    "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-priorauth-response",
                    "valueReference": {
                        "identifier":
                            {
                                "system": self.approval_request_id.response_system,
                                "value": self.approval_request_id.response_identifier
                            }
                    }
                })
            else:
                authorization_offline_date = self.authorization_offline_date if self.authorization_offline_date else \
                    self.approval_request_id.checkout_datetime
                extensions.append({
                    "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-authorization-offline-date",
                    "valueDateTime": authorization_offline_date.strftime("%Y-%m-%d")
                })

        if self.visit_id.default_invoice_id and self.visit_id.default_invoice_id.eligibility_no and self.include_references:
            if self.visit_id.default_invoice_id.last_eligibility_request_id:
                extensions.append({
                    "url": "http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-eligibility-response",
                    "valueReference": {
                        "identifier":
                            {
                                "system": self.visit_id.default_invoice_id.last_eligibility_request_id.response_system,
                                "value": self.visit_eligibility_no
                            }
                    }
                })
            else:
                extension_offline_eligibility_ref = dict(
                    url="http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-eligibility-offline-reference",
                    valueString=self.visit_id.default_invoice_id.eligibility_no
                )
                eligibilty_offline_date = self.eligibility_offline_date if self.eligibility_offline_date else \
                    self.visit_id.default_invoice_id.date

                extension_eligibility_offline_date = dict(
                    url="http://nphies.sa/fhir/ksa/nphies-fs/StructureDefinition/extension-eligibility-offline-date",
                    valueDateTime=eligibilty_offline_date.strftime("%Y-%m-%d")
                )
                extensions += [extension_offline_eligibility_ref, extension_eligibility_offline_date]
        if extensions:
            data_dict['resource'].update(extension=extensions)
        return data_dict

    def _prepare_bundle_entries(self, event_code=''):
        """
        A function to prepare bundle entries for a claim, adding additional entries and parameters.
        """
        entries = super(Claim, self)._prepare_bundle_entries(self.message_event_code)
        # TODO add additional entries , coverage resource (missing) , practitioner (done)
        # TODO add kawergs to parameters
        entries.extend(self.env["emr_nphies.base_claim_request"]._prepare_bundle_entries_common(self))
        # params = {
        #     'hcp_url': self.company.base_url,
        #     'nphies_url': self.request_manager_id.nphies_base_url
        # }
        # entries.append(self.patient_class_id._to_dict(**params))
        # physicians = self.visit_id.care_team_ids.mapped("physician_id")
        # for physician in physicians:
        #     entries.append(physician._to_dict(**params))
        #
        # if self.patient_class_id.subscriber_id and (self.patient_class_id.subscriber_id != self.visit_patient_id):
        #     entries.append(self.patient_class_id.subscriber_id.to_dict(**params))
        #
        # if self.type_code == 'vision' and self.vision_prescription_lens_line_ids:
        #     entries.append(self.vision_prescription_id._to_dict())
        #
        # if self.include_encounter:
        #     encounters = self.claim_line_ids.mapped('visit_id')
        #     if encounters:
        #         entries += [encounter._to_dict() for encounter in encounters]
        return entries

    def _proceed_response(self, response) -> bool:
        """
        A method to process the response received, extract relevant information, and update the claim details accordingly.
        """
        res = super(Claim, self)._proceed_response(response)
        if res:
            if response:
                entries = response.get('entry', [])

                focal_resource = entries[1] if len(entries) > 1 else {}
                focal_resource_details = self.request_manager_id.get_field_value(focal_resource, 'resource.extension') or []
                claim_extension_vals = self.request_manager_id._get_extension_vals(focal_resource_details)
                reissue_reason_code_id = claim_extension_vals.get('reissue_reason_id', False)
                if reissue_reason_code_id:
                    self.reissue_reason_id = self.env['emr_term.code_concept'].browse(reissue_reason_code_id)
                if focal_resource_details:
                    items = self.request_manager_id.get_field_value(focal_resource, 'resource.item') or []  # ['item']
                    for item in items:
                        item_sequence = item.get('itemSequence', 0)
                        line = self.claim_line_ids.filtered(lambda line: line.id == item_sequence)
                        # line.state = item.
                        if line:
                            extension_vals = self.request_manager_id._get_extension_vals(item.get('extension', []) or [])
                            approval_status_code_id = extension_vals.get('approval_status_code_id')
                            if approval_status_code_id:
                                line.response_outcome_id = self.env['emr_term.code_concept']. \
                                    browse(approval_status_code_id)
                            line.response_outcome_code = extension_vals.get('approval_status_code', '')
                            # line.response_outcome_code = item['extension'][0]['valueCodeableConcept']['coding'][0][
                            #     'code']
                            # line.response_outcome_id = self.env['emr_term.code_concept']. \
                            #     search(
                            #     [('code', '=', line.response_outcome_code), ('cs_oid', '=', 'adjudication-outcome')],
                            #     limit=1)

                            adjudication_vals = self.request_manager_id._get_adjudication_vals(
                                item.get('adjudication', []))

                            line.approved_payer_share_price = adjudication_vals.get('total_approved_price', 0.0)
                            line.approved_tax = adjudication_vals.get('tax', 0.0)
                            line.approved_qty = adjudication_vals.get('quantity', 0.0)
                            line.approved_benefit = adjudication_vals.get('benefit', 0.0)
                            line.approved_copay = adjudication_vals.get('copay', 0.0)
                            line.approved_patient_share = adjudication_vals.get('patient_share', 0.0)
                            line.adjudication_reasons = adjudication_vals.get("adjudication_reason", '')

                totals_adjudication_list = self.request_manager_id.get_field_value(focal_resource, 'resource.total') or []
                total_adjudication_vals = self.request_manager_id._get_adjudication_vals(totals_adjudication_list)
                if total_adjudication_vals:
                    self.approved_qty = total_adjudication_vals.get('quantity', 0)
                    self.approved_payer_share_price = total_adjudication_vals.get('total_approved_price', 0.0)
                    self.approved_tax = total_adjudication_vals.get('tax', 0.0)
                    self.approved_patient_share = total_adjudication_vals.get('patient_share', 0.0)

                self.warning_msg = ""
        return res

    # endregion

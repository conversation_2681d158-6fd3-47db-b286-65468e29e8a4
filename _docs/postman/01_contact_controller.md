# Contact Controller

### **Endpoint: `/GetContact`**

---

#### **Method Type**:

`GET`

---

#### **Description**:

GET A contact based on the contact ID (`value`).

---

#### **Request URL Example**:

`{{base_url}}/GetContact`

---

#### **Request Header Example**:

```json
{
  "Content-Type": "application/json"
}
```

---

#### **Request Parameters Example**:

| Parameter | Type    | Required | Description |
|-----------|---------|----------|-------------|
| `value`   | Integer | Yes      | Contact ID. |

---

#### **Request Body Example**:

No Request Body is required for this endpoint.

```json

```

---

#### **Request Parameters Example**:

```
value=2
```

---

#### **Request Response Example**:

- **Success Response** (Status Code: `200 Ok`):

  ```json
	 {
		"ContactID": 2,
		"FullName": "<PERSON>",
		"Tel": "(*************",
		"IDTypeID": 1,
		"IDTypeName": "we4tr4e",
		"IDNumber": "15",
		"ContactType": {
			"ContactTypeID": 1,
			"ContactTypeName": "qdwqd"
		},
		"ContactPosition": "drgzhb"
	}
		
  ```

- **Error Response** (Status Code: `200 OK`):

  If an error occurs due to an invalid Contact ID or other exceptions, the response will contain:

  ```json
  null
  ```

### **Endpoint: `/FindContacts`**

---

#### **Method Type**:

`GET`

---

#### **Description**:

This endpoint retrieves a list of contacts that match the provided search criteria (criteria).
The criteria are parsed and transformed into a search domain,
which is used to query and filter the contacts from the system.

---

#### **Request URL Example**:

`{{base_url}}/GetContacts`

---

#### **Request Header Example**:
No Request Body is required for this endpoint.
```json
{
  "Content-Type": "application/json"
}
```

---

#### **Request Parameters Example**:

| Parameter  | Type   | Required | Description             |
|------------|--------|----------|-------------------------|
| `criteria` | String | Yes      | The search criteria.    |

---

#### **Request Body Example**:
No Request Body is required for this endpoint.
```json

```

---

#### **Searchable Fields and Possible Values**

| Field Name         | Description                                      | Data Type | Possible Values                                        | Example Criteria             |
|--------------------|--------------------------------------------------|-----------|-------------------------------------------------------|------------------------------|
| `id_number`        | The contact's ID number                          | String    | Any valid ID number                                    | `criteria=id_number='12345'` |
| `sex`              | The contact's gender                             | Selection | `'male'`, `'female'`                                   | `criteria=sex='male'`        |
| `note`             | Additional notes regarding the contact           | Text      | Any valid string                                       | `criteria=note='Important'`  |
| `position`         | The position or title of the contact             | String    | Any valid string                                       | `criteria=position='Manager'`|
| `id_type_id`       | The type of ID associated with the contact       | Many2one  | Valid IDs from the `secy.id_type` model                | `criteria=id_type_id=1`      |
| `contact_type_id`  | The contact type (e.g., customer, vendor)        | Many2one  | Valid IDs from the `secy.contact_type` model           | `criteria=contact_type_id=3` |
| `title_id`         | The title associated with the contact            | Many2one  | Valid IDs from the `secy.title` model                  | `criteria=title_id=2`        |
| `appointment_ids`  | Contact's appointments                           | One2many  | Any valid appointment record                           | `criteria=appointment_ids=5` |
| `visit_ids`        | Contact's visit records                          | One2many  | Any valid visit record                                 | `criteria=visit_ids=10`      |

#### **Request Parameters Example**

```http
GET /FindContacts?criteria=sex='male'
```
This request retrieves all contacts where the gender (sex) is set to 'male'.

---
#### **Request Response Example**:

- **Success Response** (Status Code: `200 OK`):

  ```json
  [
    {
        "ContactID": 1,
        "FullName": "Azure Interior",
        "Tel": "(*************",
        "IDTypeID": 1,
        "IDTypeName": "we4tr4e",
        "IDNumber": "12",
        "ContactType": {
            "ContactTypeID": 1,
            "ContactTypeName": "qdwqd"
        },
        "ContactPosition": "edfe"
    },
    {
        "ContactID": 2,
        "FullName": "Brandon Freeman",
        "Tel": "(*************",
        "IDTypeID": 1,
        "IDTypeName": "we4tr4e",
        "IDNumber": "15",
        "ContactType": {
            "ContactTypeID": 1,
            "ContactTypeName": "qdwqd"
        },
        "ContactPosition": "drgzhb"
    }
 	]
  ```

- **Error Response** (Status Code: `200 OK`):
  If an error occurs due to an invalid Contact ID or other exceptions, the response will contain:
    ```json
    [] 
    ```

---

